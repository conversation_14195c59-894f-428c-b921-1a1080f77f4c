#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式路径规划动画
允许用户手动选择起点和终点，实时显示寻路过程
"""

import numpy as np
# import numpy.core
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
import os
# import time
import sys
from stable_baselines3 import TD3
from environment import PathPlanningEnv
from coordinate_transform import global_to_local, normalize_angle
# # 映射旧模块到新位置
# sys.modules['numpy._core'] = numpy.core
# sys.modules['numpy._core.numeric'] = numpy.core.numeric
class InteractivePathPlanning:
    def __init__(self, model_path=None):
        """
        初始化交互式路径规划系统
        
        Args:
            model_path: 模型文件路径，如果为None则自动选择最新模型
        """
        self.env = PathPlanningEnv(render_mode=None)
        
        # 自动选择最新的模型
        if model_path is None:
            model_path = self._find_latest_model()
        
        print(f"加载模型: {model_path}")
        self.model = TD3.load(model_path)
        
        # 界面相关参数
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.start_pos = None
        self.target_pos = None
        self.current_trajectory = []
        self.all_trajectories = []  # 存储所有轨迹
        self.is_selecting_start = True
        self.is_animating = False
        self.animation_obj = None
        
        # 动画相关
        self.current_step = 0
        self.trajectory_data = []
        
        # 设置界面
        self._setup_interface()
        
    def _find_latest_model(self):
        """自动找到最新的训练模型"""
        models_dir = "models"
        
        # 优先选择最新训练文件夹中的最高步数模型
        latest_training_dir = None
        latest_timestamp = 0
        
        for item in os.listdir(models_dir):
            if item.startswith("training_"):
                timestamp_str = item.replace("training_", "")
                try:
                    timestamp = int(timestamp_str.replace("_", ""))
                    if timestamp > latest_timestamp:
                        latest_timestamp = timestamp
                        latest_training_dir = item
                except:
                    continue
        
        if latest_training_dir:
            training_path = os.path.join(models_dir, latest_training_dir)
            # 查找该文件夹中的最高步数模型
            max_steps = 0
            best_model = None
            
            for file in os.listdir(training_path):
                if file.startswith("td3_pathplanning_") and file.endswith("_steps.zip"):
                    try:
                        steps = int(file.split("_")[2])
                        if steps > max_steps:
                            max_steps = steps
                            best_model = file
                    except:
                        continue
            
            if best_model:
                return os.path.join(training_path, best_model)
            else:
                # 如果没找到步数模型，使用best_model
                best_path = os.path.join(training_path, "best_model.zip")
                if os.path.exists(best_path):
                    return best_path
        
        # 备选方案：使用根目录下的模型
        fallback_models = [
            "models/best_model.zip",
            "models/final_model.zip",
            "models/td3_pathplanning_800000_steps.zip",
            "models/td3_pathplanning_400000_steps.zip"
        ]
        
        for model_path in fallback_models:
            if os.path.exists(model_path):
                return model_path
        
        raise FileNotFoundError("未找到可用的训练模型")
    
    def _setup_interface(self):
        """设置用户界面"""
        self.ax.set_xlim(0, self.env.map_size)
        self.ax.set_ylim(0, self.env.map_size)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)
        
        # 绘制障碍物
        for obstacle in self.env.obstacles:
            circle = Circle((obstacle['x'], obstacle['y']), obstacle['radius'], 
                          color='red', alpha=0.7)
            self.ax.add_patch(circle)
        
        # 设置标题和说明
        self._update_title()
        
        # 绑定鼠标点击事件
        self.fig.canvas.mpl_connect('button_press_event', self._on_click)
        
        # 添加按钮区域的文本说明
        self.ax.text(0.02, 0.02, 
                    "操作说明:\n" +
                    "1. 左键点击选择起点（绿色）\n" +
                    "2. 再次左键点击选择终点（红色）\n" +
                    "3. 选择完成后自动开始寻路动画\n" +
                    "4. 动画结束后可重新选择终点\n" +
                    "5. 关闭窗口退出程序",
                    transform=self.ax.transAxes,
                    verticalalignment='bottom',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                    fontsize=10)
    
    def _update_title(self):
        """更新标题显示当前状态"""
        if self.is_animating:
            title = "路径规划动画进行中..."
        elif self.is_selecting_start:
            title = "请点击选择起点"
        else:
            title = "请点击选择终点"
        
        self.ax.set_title(title, fontsize=14, fontweight='bold')
        
    def _on_click(self, event):
        """处理鼠标点击事件"""
        if event.inaxes != self.ax or self.is_animating:
            return
        
        x, y = event.xdata, event.ydata
        
        # 检查点击位置是否在障碍物内
        if self._is_position_valid([x, y]):
            if self.is_selecting_start:
                self.start_pos = [x, y]
                self.is_selecting_start = False
                self._draw_points()
                print(f"起点已选择: ({x:.1f}, {y:.1f})")
            else:
                self.target_pos = [x, y]
                # 检查起点和终点距离
                if np.linalg.norm(np.array(self.start_pos) - np.array(self.target_pos)) < 5:
                    print("起点和终点距离太近，请重新选择终点")
                    return
                
                self._draw_points()
                print(f"终点已选择: ({x:.1f}, {y:.1f})")
                print("开始路径规划...")
                
                # 开始路径规划
                self._start_path_planning()
        else:
            print("选择的位置与障碍物冲突，请重新选择")
    
    def _is_position_valid(self, pos):
        """检查位置是否有效（不与障碍物冲突）"""
        for obstacle in self.env.obstacles:
            distance = np.sqrt((pos[0] - obstacle['x'])**2 + (pos[1] - obstacle['y'])**2)
            if distance < obstacle['radius'] + self.env.robot_radius + 1.0:  # 1米安全距离
                return False
        return True
    
    def _draw_points(self):
        """绘制起点和终点"""
        # 清除之前的点
        for artist in self.ax.collections[len(self.env.obstacles):]:
            artist.remove()
        
        # 绘制起点
        if self.start_pos:
            self.ax.plot(self.start_pos[0], self.start_pos[1], 'o', 
                        color='green', markersize=12, label='起点')
        
        # 绘制终点
        if self.target_pos:
            self.ax.plot(self.target_pos[0], self.target_pos[1], 's', 
                        color='red', markersize=12, label='终点')
        
        self._update_title()
        plt.draw()
    
    def _start_path_planning(self):
        """开始路径规划"""
        self.is_animating = True
        self._update_title()
        
        # 设置环境
        self.env.robot_pos = np.array(self.start_pos, dtype=np.float32)
        self.env.target_pos = self.target_pos
        self.env.robot_vel = np.array([0.0, 0.0], dtype=np.float32)
        self.env.robot_angle = 0.0
        self.env.step_count = 0
        
        # 收集轨迹数据
        self._collect_trajectory_data()
        
        # 开始动画
        self._start_animation()
    
    def _collect_trajectory_data(self):
        """收集完整的轨迹数据"""
        print("正在计算路径...")
        
        self.trajectory_data = []
        obs = self.env._get_observation()
        
        done = False
        step_count = 0
        max_steps = 1000
        
        # 记录初始状态
        self.trajectory_data.append({
            'pos': self.env.robot_pos.copy(),
            'angle': self.env.robot_angle,
            'target': np.array(self.target_pos).copy(),
            'step': step_count,
            'action': np.array([0.0, 0.0])
        })
        
        while not done and step_count < max_steps:
            # 获取动作
            action, _ = self.model.predict(obs, deterministic=True)
            
            # 执行动作
            obs, reward, terminated, truncated, info = self.env.step(action)
            done = terminated or truncated
            step_count += 1
            
            # 在执行动作后记录状态
            self.trajectory_data.append({
                'pos': self.env.robot_pos.copy(),
                'angle': self.env.robot_angle,  # 现在记录的是执行动作后的角度
                'target': np.array(self.target_pos).copy(),
                'step': step_count,
                'action': action.copy()
            })
            
        # 检查路径规划是否成功
        success = info.get('success', False) if info else False
        print(f"路径规划完成: {'成功' if success else '失败'}, 总步数: {step_count}")
    
    def _start_animation(self):
        """开始动画播放"""
        self.current_step = 0
        
        # 每2步显示一次（根据用户要求）
        display_steps = list(range(0, len(self.trajectory_data), 2))
        if display_steps[-1] != len(self.trajectory_data) - 1:
            display_steps.append(len(self.trajectory_data) - 1)  # 确保显示最后一步
        
        self.animation_obj = animation.FuncAnimation(
            self.fig, self._animate_frame, frames=len(display_steps),
            interval=200, repeat=False, blit=False
        )
        
        # 动画结束后的回调
        self.animation_obj.event_source.add_callback(self._on_animation_complete)
        
        plt.draw()
    
    def _animate_frame(self, frame):
        """动画帧更新函数"""
        # 每2步显示一次
        actual_step = frame * 2
        if actual_step >= len(self.trajectory_data):
            actual_step = len(self.trajectory_data) - 1
        
        current_data = self.trajectory_data[actual_step]
        
        # 清除动态元素（保留障碍物和固定点）
        artists_to_remove = []
        for artist in self.ax.get_children():
            if hasattr(artist, 'get_label') and artist.get_label() in ['轨迹', '机器人', '方向']:
                artists_to_remove.append(artist)
        
        for artist in artists_to_remove:
            artist.remove()
        
        # 绘制轨迹（到当前步为止）
        if actual_step > 0:
            x_coords = [self.trajectory_data[i]['pos'][0] for i in range(0, actual_step + 1, 2)]
            y_coords = [self.trajectory_data[i]['pos'][1] for i in range(0, actual_step + 1, 2)]
            self.ax.plot(x_coords, y_coords, '-', color='blue', linewidth=2, 
                        alpha=0.7, label='轨迹')
        
        # 绘制机器人当前位置
        current_pos = current_data['pos']
        current_angle = current_data['angle']
        
        # 机器人本体
        robot_circle = Circle(current_pos, self.env.robot_radius, 
                            color='blue', alpha=0.8, label='机器人')
        self.ax.add_patch(robot_circle)
        
        # 机器人朝向箭头
        arrow_length = self.env.robot_radius * 2
        dx = -arrow_length * np.cos(current_angle) # 修正x方向，不知道为什么加上符号正确了
        dy = -arrow_length * np.sin(current_angle)  # 修正y方向，
        self.ax.arrow(current_pos[0], current_pos[1], dx, dy, 
                     head_width=0.3, head_length=0.2, fc='darkblue', ec='darkblue',
                     label='方向')
        
        # 重新绘制起点和终点（确保它们在最上层）
        self.ax.plot(self.start_pos[0], self.start_pos[1], 'o', 
                    color='green', markersize=12, label='起点', zorder=10)
        
        # 机器人朝向箭头后添加绘制终点时的判断
        if self.target_pos is not None:
            self.ax.plot(self.target_pos[0], self.target_pos[1], 's', 
                         color='red', markersize=12, label='终点', zorder=10)
        
        # 添加信息文本
        if self.target_pos is not None:
            distance = np.linalg.norm(current_pos - np.array(self.target_pos))
            info_text = f"步数: {actual_step + 1}/{len(self.trajectory_data)}\n"
            info_text += f"位置: ({current_pos[0]:.1f}, {current_pos[1]:.1f})\n"
            info_text += f"目标: ({self.target_pos[0]:.1f}, {self.target_pos[1]:.1f})\n"
            info_text += f"距离: {distance:.1f}"
        else:
            info_text = "等待选择终点..."
        
        # 清除之前的信息文本
        for text in self.ax.texts[1:]:  # 保留第一个说明文本
            text.remove()
        
        self.ax.text(0.98, 0.98, info_text, transform=self.ax.transAxes, 
                    verticalalignment='top', horizontalalignment='right',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8), 
                    fontsize=10)
        
        return []
    
    def _on_animation_complete(self):
        """动画完成后的处理"""
        print("动画播放完成")
        self.is_animating = False
        
        # 保存当前轨迹
        current_trajectory = [point['pos'] for point in self.trajectory_data]
        self.all_trajectories.append({
            'trajectory': current_trajectory,
            'start': self.start_pos.copy(),
            'target': self.target_pos.copy() if self.target_pos is not None else None
        })
        
        # 设置新的起点为当前终点
        self.start_pos = self.env.robot_pos.tolist()
        self.target_pos = None
        self.is_selecting_start = False  # 直接选择新终点
        
        # 绘制所有历史轨迹（淡化显示）
        self._draw_all_trajectories()
        
        self._update_title()
        print("请选择新的终点继续寻路，或关闭窗口退出")
    
    def _draw_all_trajectories(self):
        """绘制所有历史轨迹"""
        for i, traj_data in enumerate(self.all_trajectories):
            trajectory = traj_data['trajectory']
            if len(trajectory) > 1:
                x_coords = [pos[0] for pos in trajectory]
                y_coords = [pos[1] for pos in trajectory]
                self.ax.plot(x_coords, y_coords, '--', alpha=0.3, linewidth=1, 
                           color='gray', label=f'轨迹{i+1}' if i < 3 else '')
        
        plt.draw()
    
    def run(self):
        """运行交互式系统"""
        print("交互式路径规划系统已启动")
        print("请在图形界面中点击选择起点和终点")
        plt.show()

def main():
    """主函数"""
    print("启动交互式路径规划系统...")
    
    try:
        # 创建交互式系统
        planner = InteractivePathPlanning()
        
        # 运行系统
        planner.run()
        
    except Exception as e:
        print(f"系统运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()