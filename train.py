import os
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch
import gym
from stable_baselines3 import TD3
from stable_baselines3.common.noise import NormalActionNoise
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3.common.callbacks import Base<PERSON>allback, EvalCallback, CheckpointCallback, CallbackList
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.logger import configure

from environment import PathPlanningEnv
from networks import create_custom_td3_model, CustomFeatureExtractor
# from train import FixedScenariosEvalCallback

class DynamicLRCallback(BaseCallback):
    """
    动态调整学习率的回调函数
    为TD3算法的actor和critic设置不同的学习率
    支持最小学习率限制，防止学习率过度下降
    """
    def __init__(self, verbose=0, min_lr=1e-4):
        super().__init__(verbose)
        # 学习率调度参数
        self.initial_actor_lr = 5e-4
        self.initial_critic_lr = 5e-4
        self.decay_steps = 10000
        self.decay_rate_critic = 0.99
        self.decay_rate_actor = 0.98
        self.min_lr = min_lr  # 最小学习率限制
        
    def _on_training_start(self) -> None:
        """
        在训练开始时立即设置初始学习率
        """
        # 立即设置初始学习率
        self._update_learning_rates(self.initial_actor_lr, self.initial_critic_lr)
        
        if self.verbose > 0:
            print(f"Training started - Initial Actor LR: {self.initial_actor_lr:.6f}, Initial Critic LR: {self.initial_critic_lr:.6f}")
    
    def _update_learning_rates(self, actor_lr, critic_lr):
        """
        更新actor和critic的学习率
        """
        # 更新actor学习率
        if hasattr(self.model, 'actor') and hasattr(self.model.actor, 'optimizer'):
            for param_group in self.model.actor.optimizer.param_groups:
                param_group['lr'] = actor_lr
        elif hasattr(self.model.policy, 'actor_optimizer'):
            for param_group in self.model.policy.actor_optimizer.param_groups:
                param_group['lr'] = actor_lr
        
        # 更新critic学习率
        if hasattr(self.model, 'critic') and hasattr(self.model.critic, 'optimizer'):
            for param_group in self.model.critic.optimizer.param_groups:
                param_group['lr'] = critic_lr
        elif hasattr(self.model.policy, 'critic_optimizer'):
            for param_group in self.model.policy.critic_optimizer.param_groups:
                param_group['lr'] = critic_lr
        
    def _on_step(self) -> bool:
        # 每步都更新学习率以确保在日志中显示
        # 分别计算actor和critic的衰减因子
        actor_decay_factor = self.decay_rate_actor ** (self.num_timesteps / self.decay_steps)
        critic_decay_factor = self.decay_rate_critic ** (self.num_timesteps / self.decay_steps)
        
        # 计算新的学习率，并应用最小学习率限制
        new_actor_lr = max(self.initial_actor_lr * actor_decay_factor, self.min_lr)
        new_critic_lr = max(self.initial_critic_lr * critic_decay_factor, 2*self.min_lr)
        
        # 更新学习率
        self._update_learning_rates(new_actor_lr, new_critic_lr)
        
        # 记录到TensorBoard和控制台日志
        self.logger.record("train/actor_learning_rate", new_actor_lr)
        self.logger.record("train/critic_learning_rate", new_critic_lr)
        
        # 控制台输出, 每1000步输出一次  
        if self.verbose > 0 and self.n_calls % 1000 == 0:
            # 检查是否已达到最小学习率
            if new_actor_lr <= self.min_lr or new_critic_lr <= 2*self.min_lr:
                print(f"Step {self.num_timesteps}: Actor LR = {new_actor_lr:.6f} (衰减率:{self.decay_rate_actor}), Critic LR = {new_critic_lr:.6f} (衰减率:{self.decay_rate_critic}) (已达到最小学习率限制)")
            else:
                print(f"Step {self.num_timesteps}: Actor LR = {new_actor_lr:.6f} (衰减率:{self.decay_rate_actor}), Critic LR = {new_critic_lr:.6f} (衰减率:{self.decay_rate_critic})")
            
        return True

# 添加工具函数，用于设置环境的起点和终点
def set_env_scenario(env, scenario):
    """设置环境的起点和终点"""
    # 如果环境被Monitor包装，获取底层环境
    actual_env = env.env if hasattr(env, 'env') else env
    
    # 手动设置起点和终点
    actual_env.start_pos = scenario['start_pos']
    actual_env.target_pos = scenario['target_pos']
    actual_env.robot_pos = np.array(scenario['start_pos'], dtype=np.float32)
    actual_env.robot_vel = np.array([0.0, 0.0], dtype=np.float32)
    actual_env.robot_angle = np.random.uniform(-np.pi, np.pi)
    actual_env.step_count = 0
    actual_env.prev_distance = np.linalg.norm(actual_env.robot_pos - np.array(actual_env.target_pos))
    
    # 初始化雷达读数
    if hasattr(actual_env, '_get_radar_readings'):
        actual_env._last_radar_readings = actual_env._get_radar_readings()
    
    # 更新观测
    obs = actual_env._get_observation()
    return obs

class TrainingCallback:
    """训练过程监控回调"""
    def __init__(self, log_dir):
        self.log_dir = log_dir
        self.episode_rewards = []
        self.episode_lengths = []
        self.success_rates = []
        
    def __call__(self, locals_, globals_):
        # 记录训练指标
        if 'infos' in locals_:
            for info in locals_['infos']:
                if 'episode' in info:
                    self.episode_rewards.append(info['episode']['r'])
                    self.episode_lengths.append(info['episode']['l'])
        return True



def make_env(env_id, rank, seed=0, num_obstacles=8, log_dir=None, use_ocean_data=False, ocean_data_path=None):
    """创建环境的工厂函数"""
    def _init():
        env_config = {
            'map_size': 100,
            'num_obstacles': num_obstacles,
            'robot_radius': 0.5
        }
        
        # 添加海洋数据配置
        if use_ocean_data and ocean_data_path is not None:
            env_config.update({
                'use_ocean_data': True,
                'ocean_data_path': ocean_data_path,
                'ocean_grid_size': 4,
                'ocean_step_size': 0.2
            })
            
        env = PathPlanningEnv(**env_config)
        
        # 如果提供了日志目录，则使用Monitor包装器记录训练数据
        if log_dir:
            env = Monitor(env, filename=os.path.join(log_dir, f"monitor_{rank}"))
        else:
            env = Monitor(env)
        # gymnasium environments use reset(seed=seed) instead of seed()
        return env
    set_random_seed(seed)
    return _init

# 创建自定义评估回调函数，使用固定场景进行评估
class FixedScenariosEvalCallback(EvalCallback):
    def __init__(self, eval_env, fixed_scenarios, **kwargs):
        super().__init__(eval_env, **kwargs)
        self.fixed_scenarios = fixed_scenarios
        self.n_eval_episodes = min(len(fixed_scenarios), kwargs.get('n_eval_episodes', 10))
    
    def _on_step(self) -> bool:
        if self.eval_freq > 0 and self.n_calls % self.eval_freq == 0:
            # 使用固定场景进行评估
            episode_rewards, episode_lengths = [], []
            success_count = 0  # 成功次数计数器
            
            for i in range(self.n_eval_episodes):
                # 重置环境
                obs, _ = self.eval_env.envs[0].reset()
                
                # 使用工具函数设置场景
                scenario = self.fixed_scenarios[i % len(self.fixed_scenarios)]
                obs = set_env_scenario(self.eval_env.envs[0], scenario)
                
                done, state = False, None
                episode_reward = 0.0
                episode_length = 0
                success = False  # 当前回合是否成功
                
                while not done:
                    action, state = self.model.predict(
                        obs, state=state, deterministic=self.deterministic
                    )
                    obs, reward, terminated, truncated, info = self.eval_env.envs[0].step(action)
                    done = terminated or truncated
                    
                    # 检查是否到达终点（成功）
                    if terminated:
                        # 获取实际环境对象
                        actual_env = self.eval_env.envs[0].env if hasattr(self.eval_env.envs[0], 'env') else self.eval_env.envs[0]
                        # 使用环境的_is_goal_reached方法判断是否成功
                        if hasattr(actual_env, '_is_goal_reached') and actual_env._is_goal_reached():
                            success = True
                    
                    episode_reward += reward
                    episode_length += 1
                    
                    if self.render:
                        self.eval_env.render("human")
                
                episode_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                if success:
                    success_count += 1
            
            mean_reward = np.mean(episode_rewards)
            std_reward = np.std(episode_rewards)
            success_rate = (success_count / self.n_eval_episodes) * 100
            
            if self.verbose > 0:
                print(f"Eval num_timesteps={self.num_timesteps}, " 
                      f"episode_reward={mean_reward:.2f} +/- {std_reward:.2f}")
                print(f"Episode length: {np.mean(episode_lengths):.2f} +/- {np.std(episode_lengths):.2f}")
                print(f"Success rate: {success_rate:.1f}% ({success_count}/{self.n_eval_episodes})")
            
            if mean_reward > self.best_mean_reward:
                if self.verbose > 0:
                    print(f"New best mean reward: {mean_reward:.2f} vs {self.best_mean_reward:.2f}")
                if self.best_model_save_path is not None:
                    self.model.save(os.path.join(self.best_model_save_path, "best_model"))
                self.best_mean_reward = mean_reward
            
            # 记录评估结果
            if self.log_path is not None:
                save_path = os.path.join(self.log_path, "evaluations.npz")
                # 确保保存目录存在
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                np.savez(
                    save_path,
                    timesteps=self.evaluations_timesteps,
                    results=self.evaluations_results,
                    ep_lengths=self.evaluations_length,
                )
        
        return True

# 添加工具函数，用于生成固定评估场景
def generate_fixed_scenarios(env, num_scenarios=30, seed=42):
    """生成固定的评估场景"""
    np.random.seed(seed)  # 固定随机种子，确保场景可重复
    fixed_scenarios = []
    
    for _ in range(num_scenarios):
        # 生成不与障碍物碰撞的起点
        while True:
            start_pos = [
                np.random.uniform(5, env.map_size - 5),
                np.random.uniform(5, env.map_size - 5)
            ]
            collision, _ = env._check_collision(start_pos)
            if not collision:
                break
        
        # 生成不与障碍物碰撞的终点，且与起点有足够距离
        while True:
            target_pos = [
                np.random.uniform(5, env.map_size - 5),
                np.random.uniform(5, env.map_size - 5)
            ]
            collision, _ = env._check_collision(target_pos)
            if not collision and \
               np.linalg.norm(np.array(start_pos) - np.array(target_pos)) >= 20:
                break
        
        fixed_scenarios.append({
            'start_pos': start_pos,
            'target_pos': target_pos
        })
    
    return fixed_scenarios

def train_td3_agent(num_obstacles=24, use_ocean_data=False, ocean_data_path=None):
    """训练TD3智能体"""
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"./logs/td3_pathplanning_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    # 训练参数
    TOTAL_TIMESTEPS = 2000000 # 总训练步数
    N_ENVS = 8  # 并行环境数量 (使用SubprocVecEnv实现真正的多进程并行)
    EVAL_FREQ = 10000 # 评估频率
    SAVE_FREQ = 50000 #  保存频率
    
    # 设置海洋数据路径
    if ocean_data_path is None and use_ocean_data:
        ocean_data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                      'ocean_data', 'extracted_ocean_data.pkl')
        if not os.path.exists(ocean_data_path):
            print(f"警告: 未找到海洋数据文件: {ocean_data_path}")
            print("禁用海洋数据使用")
            use_ocean_data = False
    
    print("=" * 50)
    print("TD3 路径规划训练开始")
    print(f"训练步数: {TOTAL_TIMESTEPS}")
    print(f"并行环境: {N_ENVS}")
    print(f"障碍物数量: {num_obstacles}")
    print(f"使用海洋数据: {use_ocean_data}")
    if use_ocean_data:
        print(f"海洋数据路径: {ocean_data_path}")
    print(f"日志目录: {log_dir}")
    print("=" * 50)
    
    # 创建并行训练环境 (使用SubprocVecEnv实现真正的多进程并行)
    env = SubprocVecEnv([make_env("PathPlanning", i, num_obstacles=num_obstacles, log_dir=log_dir, 
                                use_ocean_data=use_ocean_data, ocean_data_path=ocean_data_path) for i in range(N_ENVS)])
    
    # 创建评估环境 (评估环境使用单进程即可)
    eval_env = DummyVecEnv([make_env("PathPlanning", 0, num_obstacles=num_obstacles, 
                                    use_ocean_data=use_ocean_data, ocean_data_path=ocean_data_path)])
    
    # 检查是否存在固定评估场景，如果不存在则创建
    fixed_scenarios_path = "fixed_evaluation_scenarios.npy"
    if os.path.exists(fixed_scenarios_path):
        print(f"加载已有的固定评估场景: {fixed_scenarios_path}")
        fixed_scenarios = np.load(fixed_scenarios_path, allow_pickle=True)
    else:
        print("创建新的固定评估场景...")
        # 创建一个临时环境来生成场景
        temp_env = PathPlanningEnv(map_size=100, num_obstacles=30, robot_radius=0.5)
        fixed_scenarios = generate_fixed_scenarios(temp_env, num_scenarios=30, seed=42)
        
        # 保存固定场景，以便将来可以重复使用
        np.save(fixed_scenarios_path, fixed_scenarios)
        print(f"已生成并保存30个固定评估场景")
        temp_env.close()
    
    # 动作噪声 (用于探索)
    n_actions = env.action_space.shape[-1]
    action_noise = NormalActionNoise(
        mean=np.zeros(n_actions), 
        sigma=0.2 * np.ones(n_actions)
    )
    
    # 创建TD3模型
    model = create_custom_td3_model(
        env=env,
        learning_rate=5e-4,  # 设置极小的初始学习率，由DynamicLRCallback控制实际学习率
        buffer_size=1000000,
        learning_starts=25000,
        batch_size=256,
        tau=0.005,
        gamma=0.99,
        train_freq=4, # 每4步更新一次Q网络
        gradient_steps=4, # 梯度更新频率，表示训练Q网络的次数
        policy_delay=3,
        target_policy_noise=0.1,
        target_noise_clip=0.5,
        device='cuda', # 自动选择设备
        tensorboard_log=log_dir  # 添加TensorBoard日志记录
    )
    
    # 配置日志记录器，确保训练日志保存到logs目录
    new_logger = configure(log_dir, ["stdout", "csv", "tensorboard"])
    model.set_logger(new_logger)
    
    # 设置动作噪声
    model.action_noise = action_noise
    
    # 创建带时间戳的models子目录用于保存本次训练的所有模型
    training_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    models_dir = os.path.join("./models", f"training_{training_timestamp}")
    os.makedirs(models_dir, exist_ok=True)
    print(f"本次训练模型将保存到: {models_dir}")
    
    # 创建固定场景评估回调
    eval_callback = FixedScenariosEvalCallback(
        eval_env,
        fixed_scenarios=fixed_scenarios,
        best_model_save_path=models_dir,  # 模型保存到models目录
        log_path=log_dir,  # 日志仍保存到logs目录
        eval_freq=EVAL_FREQ,
        deterministic=True,
        render=False,
        n_eval_episodes=30,  # 使用所有30个固定场景进行评估
        verbose=1
    )
    
    checkpoint_callback = CheckpointCallback(
        save_freq=SAVE_FREQ,
        save_path=models_dir,  # 检查点模型保存到models目录
        name_prefix='td3_pathplanning'
    )
    
    # 创建动态学习率回调（设置最小学习率为1e-4）
    lr_callback = DynamicLRCallback(verbose=1, min_lr=1e-4)
    
    callback_list = CallbackList([eval_callback, checkpoint_callback, lr_callback])
    
    # 开始训练
    print("\n开始训练...")
    try:
        model.learn(
            total_timesteps=TOTAL_TIMESTEPS,
            callback=callback_list,
            log_interval=10, # 日志记录间隔
            progress_bar=True
        )
        
        # 保存最终模型
        final_model_path = os.path.join(models_dir, "final_model")
        model.save(final_model_path)
        print(f"\n训练完成！最终模型已保存到: {final_model_path}")
        
        # 绘制训练曲线
        plot_training_results(log_dir)
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        interrupted_model_path = os.path.join(models_dir, "interrupted_model")
        model.save(interrupted_model_path)
        print(f"中断的模型已保存到: {interrupted_model_path}")
    
    finally:
        env.close()
        eval_env.close()

def plot_training_results(log_dir):
    """改进的训练结果绘制函数"""
    print(f"正在分析日志目录: {log_dir}")
    
    try:
        # 检查目录是否存在
        if not os.path.exists(log_dir):
            print(f"错误: 日志目录不存在: {log_dir}")
            return
        
        # 查找monitor文件
        monitor_files = [f for f in os.listdir(log_dir) if f.endswith('.monitor.csv')]
        print(f"找到 {len(monitor_files)} 个monitor文件")
        
        if not monitor_files:
            print("未找到训练日志文件(.monitor.csv)")
            return
        
        all_rewards = []
        all_lengths = []
        all_times = []
        successful_files = 0
        
        for file in monitor_files:
            file_path = os.path.join(log_dir, file)
            print(f"处理文件: {file}")
            
            # 检查文件是否存在和大小
            if not os.path.exists(file_path):
                print(f"  文件不存在: {file_path}")
                continue
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                print(f"  文件为空，跳过")
                continue
            
            try:
                # 使用numpy读取数据，跳过前两行（JSON元数据和列标题）
                data = np.loadtxt(file_path, delimiter=',', skiprows=2)
                
                # 处理不同的数据形状
                if len(data.shape) == 1:  # 只有一行数据
                    if len(data) >= 2:
                        all_rewards.append(data[0])
                        all_lengths.append(data[1])
                        if len(data) >= 3:
                            all_times.append(data[2])
                elif len(data.shape) == 2:  # 多行数据
                    all_rewards.extend(data[:, 0])
                    all_lengths.extend(data[:, 1])
                    if data.shape[1] >= 3:
                        all_times.extend(data[:, 2])
                
                successful_files += 1
                
            except Exception as e:
                print(f"  numpy读取失败: {e}，尝试手动解析")
                # 尝试手动解析
                try:
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[2:]  # 跳过前两行（JSON元数据和列标题）
                        parsed_count = 0
                        for line in lines:
                            if line.strip():
                                parts = line.strip().split(',')
                                if len(parts) >= 2:
                                    try:
                                        all_rewards.append(float(parts[0]))
                                        all_lengths.append(float(parts[1]))
                                        if len(parts) >= 3:
                                            all_times.append(float(parts[2]))
                                        parsed_count += 1
                                    except ValueError:
                                        continue  # 跳过无法转换的行
                    print(f"  手动解析成功，解析了 {parsed_count} 行数据")
                    successful_files += 1
                except Exception as e2:
                    print(f"  手动解析也失败: {e2}")
                    continue
        
        print(f"成功处理 {successful_files} 个文件，收集到 {len(all_rewards)} 个数据点")
        
        if len(all_rewards) == 0:
            print("无法读取任何训练数据")
            return
        
        # 数据统计
        print(f"奖励范围: {min(all_rewards):.2f} ~ {max(all_rewards):.2f}")
        print(f"平均奖励: {np.mean(all_rewards):.2f}")
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 奖励曲线
        episodes = range(len(all_rewards))
        ax1.plot(episodes, all_rewards, alpha=0.6, color='lightblue', label='原始奖励', linewidth=0.5)
        
        # 计算移动平均
        window_size = max(1, min(100, len(all_rewards) // 10))
        if window_size > 1 and len(all_rewards) >= window_size:
            smoothed_rewards = np.convolve(all_rewards, np.ones(window_size)/window_size, mode='valid')
            smooth_episodes = range(window_size-1, len(all_rewards))
            ax1.plot(smooth_episodes, smoothed_rewards, label=f'平滑奖励 (窗口={window_size})', color='blue', linewidth=2)
        
        ax1.set_xlabel('Episode')
        ax1.set_ylabel('总奖励')
        ax1.set_title('训练奖励曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 回合长度曲线
        ax2.plot(episodes, all_lengths, alpha=0.6, color='lightcoral', label='原始长度', linewidth=0.5)
        
        if window_size > 1 and len(all_lengths) >= window_size:
            smoothed_lengths = np.convolve(all_lengths, np.ones(window_size)/window_size, mode='valid')
            ax2.plot(smooth_episodes, smoothed_lengths, label=f'平滑长度 (窗口={window_size})', color='red', linewidth=2)
        
        ax2.set_xlabel('Episode')
        ax2.set_ylabel('回合长度')
        ax2.set_title('训练回合长度曲线')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(log_dir, 'training_curves.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {output_path}")
        
        plt.show()
        
        # 打印统计信息
        print("\n训练统计:")
        print(f"总回合数: {len(all_rewards)}")
        print(f"平均奖励: {np.mean(all_rewards):.2f} ± {np.std(all_rewards):.2f}")
        print(f"最高奖励: {np.max(all_rewards):.2f}")
        print(f"平均回合长度: {np.mean(all_lengths):.2f} ± {np.std(all_lengths):.2f}")
        
        # 成功率分析 (假设高奖励表示成功)
        success_threshold = 50  # 可根据实际情况调整
        success_rate = np.mean([r > success_threshold for r in all_rewards[-1000:]]) * 100
        print(f"最近1000回合成功率: {success_rate:.1f}%")
        
    except Exception as e:
        print(f"绘制训练结果时出错: {e}")

def evaluate_generalization(model_path, num_tests=30):
    """评估模型的泛化能力 - 使用30个固定场景"""
    print("\n评估模型泛化能力 (30个固定场景)...")
    
    # 加载模型
    model = TD3.load(model_path)
    
    # 创建测试环境
    env = PathPlanningEnv(map_size=100, num_obstacles=8, robot_radius=0.5)
    
    # 检查是否存在固定评估场景，如果不存在则创建
    fixed_scenarios_path = "fixed_evaluation_scenarios.npy"
    if os.path.exists(fixed_scenarios_path):
        print(f"加载已有的固定评估场景: {fixed_scenarios_path}")
        fixed_scenarios = np.load(fixed_scenarios_path, allow_pickle=True)
    else:
        print("创建新的固定评估场景...")
        fixed_scenarios = generate_fixed_scenarios(env, num_scenarios=num_tests, seed=42)
        # 保存固定场景，以便将来可以重复使用
        np.save(fixed_scenarios_path, fixed_scenarios)
        print(f"已生成并保存{num_tests}个固定评估场景")
    
    success_count = 0
    total_rewards = []
    total_steps = []
    scenario_results = []
    
    # 在每个固定场景上测试模型
    for test_idx, scenario in enumerate(fixed_scenarios):
        # 重置环境但不使用环境的随机起点和终点
        obs, _ = env.reset()
        
        # 使用工具函数设置场景
        obs = set_env_scenario(env, scenario)
        
        total_reward = 0
        steps = 0
        done = False
        success = False
        
        while not done and steps < 1000:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            steps += 1
            done = terminated or truncated
            
            if info.get('success', False):
                success_count += 1
                success = True
                break
        
        total_rewards.append(total_reward)
        total_steps.append(steps)
        
        # 记录每个场景的结果
        scenario_results.append({
            'scenario_id': test_idx,
            'success': success,
            'steps': steps,
            'reward': total_reward,
            'final_distance': info.get('distance_to_target', float('inf'))
        })
        
        if (test_idx + 1) % 10 == 0:
            print(f"测试进度: {test_idx + 1}/{num_tests}")
    
    env.close()
    
    # 打印评估结果
    print(f"\n泛化能力评估结果 (30个固定场景):")
    print(f"成功率: {success_count/num_tests*100:.1f}%")
    print(f"平均奖励: {np.mean(total_rewards):.2f} ± {np.std(total_rewards):.2f}")
    print(f"平均步数: {np.mean(total_steps):.1f} ± {np.std(total_steps):.1f}")
    
    # 保存详细的评估结果
    results_data = {
        'success_rate': success_count/num_tests,
        'avg_reward': np.mean(total_rewards),
        'avg_steps': np.mean(total_steps),
        'scenario_results': scenario_results
    }
    
    return results_data


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="TD3 Path Planning Training")
    parser.add_argument("--obstacles", type=int, default=24, help="Number of obstacles (default: 24)")
    parser.add_argument("--use_ocean_data", action="store_true", help="Use ocean data for training")
    parser.add_argument("--ocean_data_path", type=str, default=None, 
                        help="Path to ocean data file (default: ./ocean_data/extracted_ocean_data.pkl)")
    args = parser.parse_args()
    
    # 开始训练
    train_td3_agent(
        num_obstacles=args.obstacles,
        use_ocean_data=args.use_ocean_data,
        ocean_data_path=args.ocean_data_path
    )

