#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时动态路径规划系统
主要特性：
1. 实时路径规划和动画显示
2. 动态目标切换功能
3. 机器人感知范围可视化
4. 流畅的60fps实时动画
5. 支持GIF录制功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
from matplotlib.lines import Line2D
import os
import sys
import time
from datetime import datetime

# 尝试导入依赖模块，提供友好的错误提示
try:
    from stable_baselines3 import TD3
except ImportError as e:
    print("错误：缺少 stable_baselines3 模块")
    print("请安装：pip install stable-baselines3")
    sys.exit(1)

try:
    from environment import PathPlanningEnv
    from coordinate_transform import global_to_local, normalize_angle
except ImportError as e:
    print(f"错误：缺少项目依赖模块: {e}")
    print("请确保 environment.py 和 coordinate_transform.py 文件存在")
    sys.exit(1)


class GifRecorder:
    """GIF录制管理器"""
    
    def __init__(self):
        """初始化GIF录制器"""
        self.is_recording = False
        self.writer = None
        self.output_path = None
        
    def start_recording(self, fig, filename=None):
        """
        开始录制GIF
        
        Args:
            fig: matplotlib图形对象
            filename: 输出文件名，如果为None则自动生成
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"path_planning_{timestamp}.gif"
        
        self.output_path = filename
        self.writer = animation.PillowWriter(fps=20, metadata=dict(artist='PathPlanning'))
        self.is_recording = True
        print(f"开始录制GIF: {filename}")
        
    def stop_recording(self):
        """停止录制GIF"""
        if self.is_recording and self.writer:
            self.is_recording = False
            print(f"GIF录制完成: {self.output_path}")
        
    def save_frame(self, fig):
        """保存当前帧到GIF"""
        if self.is_recording and self.writer:
            self.writer.grab_frame()


class RealTimeAnimationManager:
    """实时动画管理器"""

    def __init__(self, ax, env):
        """
        初始化实时动画管理器

        Args:
            ax: matplotlib轴对象
            env: 环境对象
        """
        self.ax = ax
        self.env = env

        # 预创建图形对象以提升性能
        self._create_reusable_objects()

        # 性能监控
        self.frame_times = []
        self.last_frame_time = time.time()

        # 轨迹数据
        self.trajectory_x = []
        self.trajectory_y = []
        self.max_trajectory_length = 500  # 限制轨迹长度以提升性能
        
    def _create_reusable_objects(self):
        """预创建可复用的图形对象"""
        # 机器人感知范围（类似图片中的蓝绿色圆圈）
        self.perception_circle = Circle((0, 0), 8.0,
                                      color='lightblue', alpha=0.3,
                                      animated=True, zorder=1)
        self.ax.add_patch(self.perception_circle)

        # 机器人本体（蓝色圆点）
        self.robot_circle = Circle((0, 0), self.env.robot_radius,
                                 color='blue', alpha=0.9, animated=True, zorder=5)
        self.ax.add_patch(self.robot_circle)

        # 机器人方向指示（小箭头）
        self.robot_arrow = self.ax.annotate('', xy=(0, 0), xytext=(0, 0),
                                          arrowprops=dict(arrowstyle='->',
                                                        color='darkblue',
                                                        lw=2),
                                          animated=True, zorder=6)

        # 实时轨迹线（绿色）
        self.trajectory_line = Line2D([], [], color='green', linewidth=3,
                                    alpha=0.8, animated=True, zorder=3)
        self.ax.add_line(self.trajectory_line)

        # 起点标记（蓝色方块）
        self.start_marker = Line2D([], [], marker='s', color='blue', markersize=10,
                                 animated=True, zorder=10, linestyle='None')
        self.ax.add_line(self.start_marker)

        # 目标点标记（红色星形）
        self.target_marker = Line2D([], [], marker='*', color='red', markersize=15,
                                  animated=True, zorder=10, linestyle='None')
        self.ax.add_line(self.target_marker)

        # 信息文本
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes,
                                    verticalalignment='top', horizontalalignment='left',
                                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                                    fontsize=10, animated=True, zorder=15)

        # 存储所有动画对象
        self.animated_objects = [
            self.perception_circle, self.robot_circle, self.robot_arrow,
            self.trajectory_line, self.start_marker, self.target_marker, self.info_text
        ]
    
    def update_robot_position(self, pos, angle):
        """
        更新机器人位置和朝向

        Args:
            pos: 机器人位置 [x, y]
            angle: 机器人角度
        """
        # 更新感知范围圆圈位置
        self.perception_circle.set_center(pos)

        # 更新机器人圆圈位置
        self.robot_circle.set_center(pos)

        # 更新方向箭头
        arrow_length = self.env.robot_radius * 2
        dx = -arrow_length * np.cos(angle)
        dy = -arrow_length * np.sin(angle)

        self.robot_arrow.set_position((pos[0] + dx, pos[1] + dy))
        self.robot_arrow.xy = pos

        # 添加到实时轨迹
        self.add_trajectory_point(pos)
    
    def add_trajectory_point(self, pos):
        """
        添加轨迹点（实时更新）

        Args:
            pos: 位置 [x, y]
        """
        self.trajectory_x.append(pos[0])
        self.trajectory_y.append(pos[1])

        # 限制轨迹长度以提升性能
        if len(self.trajectory_x) > self.max_trajectory_length:
            self.trajectory_x.pop(0)
            self.trajectory_y.pop(0)

        # 更新轨迹线显示
        self.trajectory_line.set_data(self.trajectory_x, self.trajectory_y)

    def clear_trajectory(self):
        """清空轨迹"""
        self.trajectory_x.clear()
        self.trajectory_y.clear()
        self.trajectory_line.set_data([], [])

    def update_trajectory(self, x_coords, y_coords):
        """
        更新轨迹线（兼容旧接口）

        Args:
            x_coords: x坐标列表
            y_coords: y坐标列表
        """
        self.trajectory_line.set_data(x_coords, y_coords)
    
    def update_markers(self, start_pos, target_pos):
        """
        更新起点和终点标记
        
        Args:
            start_pos: 起点位置
            target_pos: 终点位置
        """
        if start_pos is not None:
            self.start_marker.set_data([start_pos[0]], [start_pos[1]])
        
        if target_pos is not None:
            self.target_marker.set_data([target_pos[0]], [target_pos[1]])
    
    def update_info_text(self, text):
        """
        更新信息文本
        
        Args:
            text: 要显示的文本内容
        """
        self.info_text.set_text(text)
    
    def get_animated_objects(self):
        """获取所有动画对象用于blitting"""
        return self.animated_objects
    
    def record_frame_time(self):
        """记录帧时间用于性能监控"""
        current_time = time.time()
        frame_time = current_time - self.last_frame_time
        self.frame_times.append(frame_time)
        self.last_frame_time = current_time
        
        # 保持最近100帧的记录
        if len(self.frame_times) > 100:
            self.frame_times.pop(0)
    
    def get_average_fps(self):
        """获取平均帧率"""
        if len(self.frame_times) < 2:
            return 0
        avg_frame_time = np.mean(self.frame_times)
        return 1.0 / avg_frame_time if avg_frame_time > 0 else 0


class RealTimeDynamicNavigation:
    """实时动态导航系统"""

    def __init__(self, model_path=None, animation_fps=60):
        """
        初始化实时动态导航系统

        Args:
            model_path: 模型文件路径，如果为None则自动选择最新模型
            animation_fps: 动画帧率，默认60fps
        """
        self.env = PathPlanningEnv(render_mode=None)

        # 自动选择最新的模型
        if model_path is None:
            model_path = self._find_latest_model()

        print(f"加载模型: {model_path}")
        self.model = TD3.load(model_path)

        # 动画参数
        self.animation_fps = animation_fps
        self.animation_interval = 1000 / animation_fps  # 转换为毫秒

        # 界面相关参数
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.start_pos = None
        self.target_pos = None
        self.current_pos = None
        self.current_angle = 0.0
        self.is_selecting_start = True
        self.is_running = False
        self.animation_obj = None
        self.obs = None

        # 实时控制参数
        self.step_counter = 0
        self.max_steps = 1000
        
        # 初始化管理器
        self.animation_manager = None
        self.gif_recorder = GifRecorder()
        
        # 设置界面
        self._setup_interface()
        
    def _find_latest_model(self):
        """自动找到最新的训练模型"""
        models_dir = "models"
        
        # 优先选择最新训练文件夹中的最高步数模型
        latest_training_dir = None
        latest_timestamp = 0
        
        for item in os.listdir(models_dir):
            if item.startswith("training_"):
                timestamp_str = item.replace("training_", "")
                try:
                    timestamp = int(timestamp_str.replace("_", ""))
                    if timestamp > latest_timestamp:
                        latest_timestamp = timestamp
                        latest_training_dir = item
                except:
                    continue
        
        if latest_training_dir:
            training_path = os.path.join(models_dir, latest_training_dir)
            # 查找该文件夹中的最高步数模型
            max_steps = 0
            best_model = None
            
            for file in os.listdir(training_path):
                if file.startswith("td3_pathplanning_") and file.endswith("_steps.zip"):
                    try:
                        steps = int(file.split("_")[2])
                        if steps > max_steps:
                            max_steps = steps
                            best_model = file
                    except:
                        continue
            
            if best_model:
                return os.path.join(training_path, best_model)
            else:
                # 如果没找到步数模型，使用best_model
                best_path = os.path.join(training_path, "best_model.zip")
                if os.path.exists(best_path):
                    return best_path
        
        # 备选方案：使用根目录下的模型
        fallback_models = [
            "models/best_model.zip",
            "models/final_model.zip", 
            "models/td3_pathplanning_800000_steps.zip",
            "models/td3_pathplanning_400000_steps.zip"
        ]
        
        for model_path in fallback_models:
            if os.path.exists(model_path):
                return model_path
        
        raise FileNotFoundError("未找到可用的训练模型")

    def _setup_interface(self):
        """设置用户界面"""
        self.ax.set_xlim(0, self.env.map_size)
        self.ax.set_ylim(0, self.env.map_size)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)

        # 绘制障碍物
        for obstacle in self.env.obstacles:
            circle = Circle((obstacle['x'], obstacle['y']), obstacle['radius'],
                          color='red', alpha=0.7)
            self.ax.add_patch(circle)

        # 初始化实时动画管理器
        self.animation_manager = RealTimeAnimationManager(self.ax, self.env)

        # 环境状态
        self.obs = None

        # 设置标题和说明
        self._update_title()

        # 绑定鼠标点击事件
        self.fig.canvas.mpl_connect('button_press_event', self._on_click)

        # 添加按钮区域的文本说明
        self.ax.text(0.02, 0.02,
                    "操作说明:\n" +
                    "1. 左键点击选择起点（绿色）\n" +
                    "2. 再次左键点击选择终点（红色）\n" +
                    "3. 选择完成后自动开始寻路动画\n" +
                    "4. 动画结束后可重新选择终点\n" +
                    "5. 按'r'键开始/停止录制GIF\n" +
                    "6. 关闭窗口退出程序",
                    transform=self.ax.transAxes,
                    verticalalignment='bottom',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                    fontsize=10)

        # 绑定键盘事件
        self.fig.canvas.mpl_connect('key_press_event', self._on_key_press)

    def _on_key_press(self, event):
        """处理键盘事件"""
        if event.key == 'r':
            if not self.gif_recorder.is_recording:
                self.gif_recorder.start_recording(self.fig)
            else:
                self.gif_recorder.stop_recording()
        elif event.key == '+' or event.key == '=':
            # 增加动画速度
            self.animation_fps = min(60, self.animation_fps + 5)
            self.animation_interval = 1000 / self.animation_fps
            print(f"动画帧率: {self.animation_fps} fps")
        elif event.key == '-':
            # 减少动画速度
            self.animation_fps = max(5, self.animation_fps - 5)
            self.animation_interval = 1000 / self.animation_fps
            print(f"动画帧率: {self.animation_fps} fps")

    def _update_title(self):
        """更新标题显示当前状态"""
        if self.is_running:
            fps_info = f" (FPS: {self.animation_fps})"
            if self.animation_manager:
                actual_fps = self.animation_manager.get_average_fps()
                if actual_fps > 0:
                    fps_info = f" (目标: {self.animation_fps}fps, 实际: {actual_fps:.1f}fps)"
            title = f"实时动态导航中...{fps_info}"
        elif self.is_selecting_start:
            title = "NavRL Dynamic Goal Navigation - 请点击选择起点"
        else:
            title = "NavRL Dynamic Goal Navigation - 点击选择目标点（可动态切换）"

        if self.gif_recorder.is_recording:
            title += " [录制中]"

        self.ax.set_title(title, fontsize=14, fontweight='bold')

    def _on_click(self, event):
        """处理鼠标点击事件"""
        if event.inaxes != self.ax:
            return

        x, y = event.xdata, event.ydata

        # 检查点击位置是否在障碍物内
        if self._is_position_valid([x, y]):
            if self.is_selecting_start:
                self.start_pos = [x, y]
                self.current_pos = np.array(self.start_pos, dtype=np.float32)
                self.is_selecting_start = False
                self._update_markers()
                print(f"起点已选择: ({x:.1f}, {y:.1f})")

                # 初始化环境
                self._initialize_robot_state()
            else:
                # 动态更新目标点
                self.target_pos = [x, y]
                self._update_markers()
                print(f"新目标已选择: ({x:.1f}, {y:.1f})")

                # 如果还没开始运行，则开始实时导航
                if not self.is_running:
                    print("开始实时导航...")
                    self._start_real_time_navigation()
        else:
            print("选择的位置与障碍物冲突，请重新选择")

    def _is_position_valid(self, pos):
        """检查位置是否有效（不与障碍物冲突）"""
        for obstacle in self.env.obstacles:
            distance = np.sqrt((pos[0] - obstacle['x'])**2 + (pos[1] - obstacle['y'])**2)
            if distance < obstacle['radius'] + self.env.robot_radius + 1.0:  # 1米安全距离
                return False
        return True

    def _update_markers(self):
        """更新起点和终点标记"""
        self.animation_manager.update_markers(self.start_pos, self.target_pos)
        self._update_title()
        plt.draw()

    def _initialize_robot_state(self):
        """初始化机器人状态"""
        self.env.robot_pos = np.array(self.start_pos, dtype=np.float32)
        self.env.robot_vel = np.array([0.0, 0.0], dtype=np.float32)
        self.env.robot_angle = 0.0
        self.env.step_count = 0
        self.current_angle = 0.0
        self.step_counter = 0

        # 清空轨迹
        self.animation_manager.clear_trajectory()

        # 更新显示
        self.animation_manager.update_robot_position(self.current_pos, self.current_angle)
        plt.draw()

    def _start_real_time_navigation(self):
        """开始实时导航"""
        if self.target_pos is None:
            print("请先选择目标点")
            return

        self.is_running = True
        self._update_title()

        # 设置目标
        self.env.target_pos = self.target_pos

        # 获取初始观测
        self.obs = self.env.reset()[0]

        # 开始实时动画循环
        self.animation_obj = animation.FuncAnimation(
            self.fig, self._real_time_step, interval=self.animation_interval,
            repeat=True, blit=False
        )

        print("实时导航已启动")

    def _real_time_step(self, frame):
        """实时导航步进函数"""
        if not self.is_running or self.target_pos is None:
            return []

        # 检查是否达到最大步数
        if self.step_counter >= self.max_steps:
            print("达到最大步数，停止导航")
            self._stop_navigation()
            return []

        try:
            # 更新目标位置（支持动态目标）
            self.env.target_pos = self.target_pos

            # 使用模型预测动作
            action, _ = self.model.predict(self.obs, deterministic=True)

            # 执行动作
            self.obs, _, terminated, truncated, info = self.env.step(action)
            done = terminated or truncated
            self.step_counter += 1

            # 更新当前位置和角度
            self.current_pos = self.env.robot_pos.copy()
            self.current_angle = self.env.robot_angle

            # 更新动画显示
            self.animation_manager.update_robot_position(self.current_pos, self.current_angle)
            self.animation_manager.update_markers(self.start_pos, self.target_pos)

            # 更新信息显示
            if self.target_pos is not None:
                distance = np.linalg.norm(self.current_pos - np.array(self.target_pos))
                current_fps = self.animation_manager.get_average_fps()
                info_text = f"步数: {self.step_counter}\n"
                info_text += f"位置: ({self.current_pos[0]:.1f}, {self.current_pos[1]:.1f})\n"
                info_text += f"目标: ({self.target_pos[0]:.1f}, {self.target_pos[1]:.1f})\n"
                info_text += f"距离: {distance:.1f}\n"
                info_text += f"FPS: {current_fps:.1f}"
                self.animation_manager.update_info_text(info_text)

            # 检查是否到达目标
            if done:
                if terminated:
                    print(f"成功到达目标！总步数: {self.step_counter}")
                else:
                    print(f"导航超时，总步数: {self.step_counter}")

                # 不停止导航，继续等待新目标
                print("可以点击选择新目标继续导航")

            # 更新标题
            self._update_title()

            # GIF录制
            if self.gif_recorder.is_recording:
                self.gif_recorder.save_frame(self.fig)

        except Exception as e:
            print(f"导航步进出错: {e}")
            self._stop_navigation()

        return []

    def _stop_navigation(self):
        """停止导航"""
        self.is_running = False
        if self.animation_obj:
            self.animation_obj.event_source.stop()
            self.animation_obj = None
        self._update_title()
        print("导航已停止")




    def run(self):
        """运行实时动态导航系统"""
        print("🚀 实时动态导航系统已启动")
        print("✨ 主要特性:")
        print(f"- 实时动画帧率: {self.animation_fps} fps")
        print("- 动态目标切换功能")
        print("- 机器人感知范围可视化")
        print("- 实时轨迹显示")
        print("- 支持GIF录制功能")
        print("\n🎮 操作说明:")
        print("- 首次点击：选择起点（蓝色方块）")
        print("- 再次点击：选择目标点（红色星形）")
        print("- 导航过程中可随时点击切换新目标")
        print("- 按'r'键开始/停止录制GIF")
        print("- 按'+/-'键调整动画速度")
        print("- 关闭窗口退出程序")
        plt.show()


def main():
    """主函数"""
    print("🎯 启动实时动态导航系统...")

    try:
        # 创建实时动态导航系统
        planner = RealTimeDynamicNavigation(animation_fps=60)

        # 运行系统
        planner.run()

    except Exception as e:
        print(f"系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
