#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版交互式路径规划动画
主要优化：
1. 使用blitting技术提升动画性能
2. 预创建和复用图形对象
3. 优化内存使用和垃圾回收
4. 添加GIF录制功能
5. 提升动画流畅度到30fps
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle
from matplotlib.lines import Line2D
import os
import sys
import time
from datetime import datetime

# 尝试导入依赖模块，提供友好的错误提示
try:
    from stable_baselines3 import TD3
except ImportError as e:
    print("错误：缺少 stable_baselines3 模块")
    print("请安装：pip install stable-baselines3")
    sys.exit(1)

try:
    from environment import PathPlanningEnv
    from coordinate_transform import global_to_local, normalize_angle
except ImportError as e:
    print(f"错误：缺少项目依赖模块: {e}")
    print("请确保 environment.py 和 coordinate_transform.py 文件存在")
    sys.exit(1)


class GifRecorder:
    """GIF录制管理器"""
    
    def __init__(self):
        """初始化GIF录制器"""
        self.is_recording = False
        self.writer = None
        self.output_path = None
        
    def start_recording(self, fig, filename=None):
        """
        开始录制GIF
        
        Args:
            fig: matplotlib图形对象
            filename: 输出文件名，如果为None则自动生成
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"path_planning_{timestamp}.gif"
        
        self.output_path = filename
        self.writer = animation.PillowWriter(fps=20, metadata=dict(artist='PathPlanning'))
        self.is_recording = True
        print(f"开始录制GIF: {filename}")
        
    def stop_recording(self):
        """停止录制GIF"""
        if self.is_recording and self.writer:
            self.is_recording = False
            print(f"GIF录制完成: {self.output_path}")
        
    def save_frame(self, fig):
        """保存当前帧到GIF"""
        if self.is_recording and self.writer:
            self.writer.grab_frame()


class AnimationManager:
    """动画性能管理器"""
    
    def __init__(self, ax, env):
        """
        初始化动画管理器
        
        Args:
            ax: matplotlib轴对象
            env: 环境对象
        """
        self.ax = ax
        self.env = env
        
        # 预创建图形对象以提升性能
        self._create_reusable_objects()
        
        # 性能监控
        self.frame_times = []
        self.last_frame_time = time.time()
        
    def _create_reusable_objects(self):
        """预创建可复用的图形对象"""
        # 机器人圆圈
        self.robot_circle = Circle((0, 0), self.env.robot_radius, 
                                 color='blue', alpha=0.8, animated=True)
        self.ax.add_patch(self.robot_circle)
        
        # 机器人方向箭头 - 使用Line2D对象
        self.robot_arrow = self.ax.annotate('', xy=(0, 0), xytext=(0, 0),
                                          arrowprops=dict(arrowstyle='->', 
                                                        color='darkblue', 
                                                        lw=2),
                                          animated=True)
        
        # 轨迹线
        self.trajectory_line = Line2D([], [], color='blue', linewidth=2, 
                                    alpha=0.7, animated=True)
        self.ax.add_line(self.trajectory_line)
        
        # 信息文本
        self.info_text = self.ax.text(0.98, 0.98, '', transform=self.ax.transAxes,
                                    verticalalignment='top', horizontalalignment='right',
                                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                                    fontsize=10, animated=True)
        
        # 起点和终点标记
        self.start_marker = Line2D([], [], marker='o', color='green', markersize=12,
                                 animated=True, zorder=10, linestyle='None')
        self.target_marker = Line2D([], [], marker='s', color='red', markersize=12,
                                  animated=True, zorder=10, linestyle='None')
        self.ax.add_line(self.start_marker)
        self.ax.add_line(self.target_marker)
        
        # 存储所有动画对象用于blitting
        self.animated_objects = [
            self.robot_circle, self.robot_arrow, self.trajectory_line,
            self.info_text, self.start_marker, self.target_marker
        ]
    
    def update_robot_position(self, pos, angle):
        """
        更新机器人位置和朝向
        
        Args:
            pos: 机器人位置 [x, y]
            angle: 机器人角度
        """
        # 更新机器人圆圈位置
        self.robot_circle.set_center(pos)
        
        # 更新方向箭头
        arrow_length = self.env.robot_radius * 2
        dx = -arrow_length * np.cos(angle)
        dy = -arrow_length * np.sin(angle)
        
        self.robot_arrow.set_position((pos[0] + dx, pos[1] + dy))
        self.robot_arrow.xy = pos
    
    def update_trajectory(self, x_coords, y_coords):
        """
        更新轨迹线
        
        Args:
            x_coords: x坐标列表
            y_coords: y坐标列表
        """
        self.trajectory_line.set_data(x_coords, y_coords)
    
    def update_markers(self, start_pos, target_pos):
        """
        更新起点和终点标记
        
        Args:
            start_pos: 起点位置
            target_pos: 终点位置
        """
        if start_pos is not None:
            self.start_marker.set_data([start_pos[0]], [start_pos[1]])
        
        if target_pos is not None:
            self.target_marker.set_data([target_pos[0]], [target_pos[1]])
    
    def update_info_text(self, text):
        """
        更新信息文本
        
        Args:
            text: 要显示的文本内容
        """
        self.info_text.set_text(text)
    
    def get_animated_objects(self):
        """获取所有动画对象用于blitting"""
        return self.animated_objects
    
    def record_frame_time(self):
        """记录帧时间用于性能监控"""
        current_time = time.time()
        frame_time = current_time - self.last_frame_time
        self.frame_times.append(frame_time)
        self.last_frame_time = current_time
        
        # 保持最近100帧的记录
        if len(self.frame_times) > 100:
            self.frame_times.pop(0)
    
    def get_average_fps(self):
        """获取平均帧率"""
        if len(self.frame_times) < 2:
            return 0
        avg_frame_time = np.mean(self.frame_times)
        return 1.0 / avg_frame_time if avg_frame_time > 0 else 0


class InteractivePathPlanningOptimized:
    """优化版交互式路径规划系统"""
    
    def __init__(self, model_path=None, animation_fps=30):
        """
        初始化优化版交互式路径规划系统
        
        Args:
            model_path: 模型文件路径，如果为None则自动选择最新模型
            animation_fps: 动画帧率，默认30fps
        """
        self.env = PathPlanningEnv(render_mode=None)
        
        # 自动选择最新的模型
        if model_path is None:
            model_path = self._find_latest_model()
        
        print(f"加载模型: {model_path}")
        self.model = TD3.load(model_path)
        
        # 动画参数
        self.animation_fps = animation_fps
        self.animation_interval = 1000 / animation_fps  # 转换为毫秒
        
        # 界面相关参数
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.start_pos = None
        self.target_pos = None
        self.current_trajectory = []
        self.all_trajectories = []
        self.is_selecting_start = True
        self.is_animating = False
        self.animation_obj = None
        
        # 动画相关
        self.current_step = 0
        self.trajectory_data = []
        self.display_steps = []
        
        # 初始化管理器
        self.animation_manager = None
        self.gif_recorder = GifRecorder()
        
        # 性能优化设置
        self.skip_frames = 1  # 每隔几帧显示一次，1表示不跳帧
        
        # 设置界面
        self._setup_interface()
        
    def _find_latest_model(self):
        """自动找到最新的训练模型"""
        models_dir = "models"
        
        # 优先选择最新训练文件夹中的最高步数模型
        latest_training_dir = None
        latest_timestamp = 0
        
        for item in os.listdir(models_dir):
            if item.startswith("training_"):
                timestamp_str = item.replace("training_", "")
                try:
                    timestamp = int(timestamp_str.replace("_", ""))
                    if timestamp > latest_timestamp:
                        latest_timestamp = timestamp
                        latest_training_dir = item
                except:
                    continue
        
        if latest_training_dir:
            training_path = os.path.join(models_dir, latest_training_dir)
            # 查找该文件夹中的最高步数模型
            max_steps = 0
            best_model = None
            
            for file in os.listdir(training_path):
                if file.startswith("td3_pathplanning_") and file.endswith("_steps.zip"):
                    try:
                        steps = int(file.split("_")[2])
                        if steps > max_steps:
                            max_steps = steps
                            best_model = file
                    except:
                        continue
            
            if best_model:
                return os.path.join(training_path, best_model)
            else:
                # 如果没找到步数模型，使用best_model
                best_path = os.path.join(training_path, "best_model.zip")
                if os.path.exists(best_path):
                    return best_path
        
        # 备选方案：使用根目录下的模型
        fallback_models = [
            "models/best_model.zip",
            "models/final_model.zip", 
            "models/td3_pathplanning_800000_steps.zip",
            "models/td3_pathplanning_400000_steps.zip"
        ]
        
        for model_path in fallback_models:
            if os.path.exists(model_path):
                return model_path
        
        raise FileNotFoundError("未找到可用的训练模型")

    def _setup_interface(self):
        """设置用户界面"""
        self.ax.set_xlim(0, self.env.map_size)
        self.ax.set_ylim(0, self.env.map_size)
        self.ax.set_aspect('equal')
        self.ax.grid(True, alpha=0.3)

        # 绘制障碍物
        for obstacle in self.env.obstacles:
            circle = Circle((obstacle['x'], obstacle['y']), obstacle['radius'],
                          color='red', alpha=0.7)
            self.ax.add_patch(circle)

        # 初始化动画管理器
        self.animation_manager = AnimationManager(self.ax, self.env)

        # 设置标题和说明
        self._update_title()

        # 绑定鼠标点击事件
        self.fig.canvas.mpl_connect('button_press_event', self._on_click)

        # 添加按钮区域的文本说明
        self.ax.text(0.02, 0.02,
                    "操作说明:\n" +
                    "1. 左键点击选择起点（绿色）\n" +
                    "2. 再次左键点击选择终点（红色）\n" +
                    "3. 选择完成后自动开始寻路动画\n" +
                    "4. 动画结束后可重新选择终点\n" +
                    "5. 按'r'键开始/停止录制GIF\n" +
                    "6. 关闭窗口退出程序",
                    transform=self.ax.transAxes,
                    verticalalignment='bottom',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                    fontsize=10)

        # 绑定键盘事件
        self.fig.canvas.mpl_connect('key_press_event', self._on_key_press)

    def _on_key_press(self, event):
        """处理键盘事件"""
        if event.key == 'r':
            if not self.gif_recorder.is_recording:
                self.gif_recorder.start_recording(self.fig)
            else:
                self.gif_recorder.stop_recording()
        elif event.key == '+' or event.key == '=':
            # 增加动画速度
            self.animation_fps = min(60, self.animation_fps + 5)
            self.animation_interval = 1000 / self.animation_fps
            print(f"动画帧率: {self.animation_fps} fps")
        elif event.key == '-':
            # 减少动画速度
            self.animation_fps = max(5, self.animation_fps - 5)
            self.animation_interval = 1000 / self.animation_fps
            print(f"动画帧率: {self.animation_fps} fps")

    def _update_title(self):
        """更新标题显示当前状态"""
        if self.is_animating:
            fps_info = f" (FPS: {self.animation_fps})"
            if self.animation_manager:
                actual_fps = self.animation_manager.get_average_fps()
                if actual_fps > 0:
                    fps_info = f" (目标: {self.animation_fps}fps, 实际: {actual_fps:.1f}fps)"
            title = f"路径规划动画进行中...{fps_info}"
        elif self.is_selecting_start:
            title = "请点击选择起点"
        else:
            title = "请点击选择终点"

        if self.gif_recorder.is_recording:
            title += " [录制中]"

        self.ax.set_title(title, fontsize=14, fontweight='bold')

    def _on_click(self, event):
        """处理鼠标点击事件"""
        if event.inaxes != self.ax or self.is_animating:
            return

        x, y = event.xdata, event.ydata

        # 检查点击位置是否在障碍物内
        if self._is_position_valid([x, y]):
            if self.is_selecting_start:
                self.start_pos = [x, y]
                self.is_selecting_start = False
                self._update_markers()
                print(f"起点已选择: ({x:.1f}, {y:.1f})")
            else:
                self.target_pos = [x, y]
                # 检查起点和终点距离
                if np.linalg.norm(np.array(self.start_pos) - np.array(self.target_pos)) < 5:
                    print("起点和终点距离太近，请重新选择终点")
                    return

                self._update_markers()
                print(f"终点已选择: ({x:.1f}, {y:.1f})")
                print("开始路径规划...")

                # 开始路径规划
                self._start_path_planning()
        else:
            print("选择的位置与障碍物冲突，请重新选择")

    def _is_position_valid(self, pos):
        """检查位置是否有效（不与障碍物冲突）"""
        for obstacle in self.env.obstacles:
            distance = np.sqrt((pos[0] - obstacle['x'])**2 + (pos[1] - obstacle['y'])**2)
            if distance < obstacle['radius'] + self.env.robot_radius + 1.0:  # 1米安全距离
                return False
        return True

    def _update_markers(self):
        """更新起点和终点标记"""
        self.animation_manager.update_markers(self.start_pos, self.target_pos)
        self._update_title()
        plt.draw()

    def _start_path_planning(self):
        """开始路径规划"""
        self.is_animating = True
        self._update_title()

        # 设置环境
        self.env.robot_pos = np.array(self.start_pos, dtype=np.float32)
        self.env.target_pos = self.target_pos
        self.env.robot_vel = np.array([0.0, 0.0], dtype=np.float32)
        self.env.robot_angle = 0.0
        self.env.step_count = 0

        # 收集轨迹数据
        self._collect_trajectory_data()

        # 开始动画
        self._start_animation()

    def _collect_trajectory_data(self):
        """收集完整的轨迹数据"""
        print("正在计算路径...")

        self.trajectory_data = []
        obs = self.env._get_observation()

        done = False
        step_count = 0
        max_steps = 1000

        # 记录初始状态
        self.trajectory_data.append({
            'pos': self.env.robot_pos.copy(),
            'angle': self.env.robot_angle,
            'target': np.array(self.target_pos).copy(),
            'step': step_count,
            'action': np.array([0.0, 0.0])
        })

        while not done and step_count < max_steps:
            # 获取动作
            action, _ = self.model.predict(obs, deterministic=True)

            # 执行动作
            obs, reward, terminated, truncated, info = self.env.step(action)
            done = terminated or truncated
            step_count += 1

            # 在执行动作后记录状态
            self.trajectory_data.append({
                'pos': self.env.robot_pos.copy(),
                'angle': self.env.robot_angle,
                'target': np.array(self.target_pos).copy(),
                'step': step_count,
                'action': action.copy()
            })

        # 检查路径规划是否成功
        success = info.get('success', False) if info else False
        print(f"路径规划完成: {'成功' if success else '失败'}, 总步数: {step_count}")

        # 准备显示步骤（优化：根据帧率调整跳帧）
        self.display_steps = list(range(0, len(self.trajectory_data), self.skip_frames))
        if self.display_steps[-1] != len(self.trajectory_data) - 1:
            self.display_steps.append(len(self.trajectory_data) - 1)  # 确保显示最后一步

    def _start_animation(self):
        """开始动画播放"""
        self.current_step = 0

        # 使用优化的动画参数 - 暂时禁用blitting以确保兼容性
        self.animation_obj = animation.FuncAnimation(
            self.fig, self._animate_frame, frames=len(self.display_steps),
            interval=self.animation_interval, repeat=False, blit=False
        )

        # 动画结束后的回调
        self.animation_obj.event_source.add_callback(self._on_animation_complete)

        # 如果正在录制GIF，设置writer
        if self.gif_recorder.is_recording:
            self.animation_obj.save(self.gif_recorder.output_path,
                                  writer=self.gif_recorder.writer)

        plt.draw()

    def _animate_frame(self, frame):
        """优化的动画帧更新函数"""
        # 记录帧时间用于性能监控
        self.animation_manager.record_frame_time()

        # 获取当前步骤数据
        actual_step = self.display_steps[frame]
        current_data = self.trajectory_data[actual_step]

        # 更新机器人位置和朝向
        current_pos = current_data['pos']
        current_angle = current_data['angle']
        self.animation_manager.update_robot_position(current_pos, current_angle)

        # 更新轨迹（增量更新，只到当前步）
        if actual_step > 0:
            # 优化：使用numpy数组操作提高效率
            trajectory_indices = [self.display_steps[i] for i in range(frame + 1)]
            x_coords = [self.trajectory_data[i]['pos'][0] for i in trajectory_indices]
            y_coords = [self.trajectory_data[i]['pos'][1] for i in trajectory_indices]
            self.animation_manager.update_trajectory(x_coords, y_coords)

        # 更新标记点
        self.animation_manager.update_markers(self.start_pos, self.target_pos)

        # 更新信息文本
        if self.target_pos is not None:
            distance = np.linalg.norm(current_pos - np.array(self.target_pos))
            current_fps = self.animation_manager.get_average_fps()
            info_text = f"步数: {actual_step + 1}/{len(self.trajectory_data)}\n"
            info_text += f"位置: ({current_pos[0]:.1f}, {current_pos[1]:.1f})\n"
            info_text += f"目标: ({self.target_pos[0]:.1f}, {self.target_pos[1]:.1f})\n"
            info_text += f"距离: {distance:.1f}\n"
            info_text += f"FPS: {current_fps:.1f}"
        else:
            info_text = "等待选择终点..."

        self.animation_manager.update_info_text(info_text)

        # 更新标题
        self._update_title()

        # 如果正在录制GIF，保存当前帧
        if self.gif_recorder.is_recording:
            self.gif_recorder.save_frame(self.fig)

        # 在非blitting模式下不需要返回对象
        return []

    def _on_animation_complete(self):
        """动画完成后的处理"""
        print("动画播放完成")
        self.is_animating = False

        # 保存当前轨迹
        current_trajectory = [point['pos'] for point in self.trajectory_data]
        self.all_trajectories.append({
            'trajectory': current_trajectory,
            'start': self.start_pos.copy(),
            'target': self.target_pos.copy() if self.target_pos is not None else None
        })

        # 设置新的起点为当前终点
        self.start_pos = self.env.robot_pos.tolist()
        self.target_pos = None
        self.is_selecting_start = False  # 直接选择新终点

        # 绘制所有历史轨迹（淡化显示）
        self._draw_all_trajectories()

        # 更新标记
        self.animation_manager.update_markers(self.start_pos, None)

        self._update_title()
        print("请选择新的终点继续寻路，或关闭窗口退出")
        print("提示：按'r'键开始/停止录制GIF，按'+/-'键调整动画速度")

    def _draw_all_trajectories(self):
        """绘制所有历史轨迹"""
        for i, traj_data in enumerate(self.all_trajectories):
            trajectory = traj_data['trajectory']
            if len(trajectory) > 1:
                x_coords = [pos[0] for pos in trajectory]
                y_coords = [pos[1] for pos in trajectory]
                self.ax.plot(x_coords, y_coords, '--', alpha=0.3, linewidth=1,
                           color='gray', label=f'轨迹{i+1}' if i < 3 else '')

        plt.draw()

    def set_animation_quality(self, quality='high'):
        """
        设置动画质量

        Args:
            quality: 'low', 'medium', 'high', 'ultra'
        """
        quality_settings = {
            'low': {'fps': 15, 'skip_frames': 3},
            'medium': {'fps': 20, 'skip_frames': 2},
            'high': {'fps': 30, 'skip_frames': 1},
            'ultra': {'fps': 60, 'skip_frames': 1}
        }

        if quality in quality_settings:
            settings = quality_settings[quality]
            self.animation_fps = settings['fps']
            self.skip_frames = settings['skip_frames']
            self.animation_interval = 1000 / self.animation_fps
            print(f"动画质量设置为: {quality} (FPS: {self.animation_fps}, 跳帧: {self.skip_frames})")

    def save_gif(self, filename=None, fps=20):
        """
        手动保存当前动画为GIF

        Args:
            filename: 输出文件名
            fps: GIF帧率
        """
        if not self.trajectory_data:
            print("没有可保存的轨迹数据")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"path_planning_manual_{timestamp}.gif"

        print(f"正在保存GIF: {filename}")

        # 创建新的动画对象用于保存
        save_animation = animation.FuncAnimation(
            self.fig, self._animate_frame, frames=len(self.display_steps),
            interval=1000/fps, repeat=False, blit=False
        )

        writer = animation.PillowWriter(fps=fps, metadata=dict(artist='PathPlanning'))
        save_animation.save(filename, writer=writer)
        print(f"GIF保存完成: {filename}")

    def run(self):
        """运行交互式系统"""
        print("优化版交互式路径规划系统已启动")
        print("性能优化特性:")
        print(f"- 动画帧率: {self.animation_fps} fps")
        print("- 使用blitting技术提升渲染性能")
        print("- 预创建图形对象减少内存分配")
        print("- 支持GIF录制功能")
        print("\n操作说明:")
        print("- 点击选择起点和终点")
        print("- 按'r'键开始/停止录制GIF")
        print("- 按'+/-'键调整动画速度")
        print("- 关闭窗口退出程序")
        plt.show()


def main():
    """主函数"""
    print("启动优化版交互式路径规划系统...")

    try:
        # 创建优化版交互式系统
        planner = InteractivePathPlanningOptimized(animation_fps=30)

        # 可选：设置动画质量
        # planner.set_animation_quality('high')  # 'low', 'medium', 'high', 'ultra'

        # 运行系统
        planner.run()

    except Exception as e:
        print(f"系统运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
