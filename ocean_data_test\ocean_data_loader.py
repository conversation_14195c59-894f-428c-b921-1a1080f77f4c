import numpy as np
import xarray as xr
import os
from typing import Tuple, Dict, Optional

class BilinearInterpolator:
    """
    双线性插值器 - 替代SciPy的RegularGridInterpolator
    使用纯NumPy实现，避免版本兼容性问题
    """
    
    def __init__(self, points, values, fill_value=0):
        """
        初始化双线性插值器
        
        Args:
            points: 坐标点元组 (lat_array, lon_array)
            values: 对应的数值数组
            fill_value: 超出边界时的填充值
        """
        self.lat_coords = points[0]
        self.lon_coords = points[1]
        self.values = values
        self.fill_value = fill_value
        
        # 确保坐标是排序的
        if not np.all(np.diff(self.lat_coords) >= 0):
            raise ValueError("纬度坐标必须是递增排序的")
        if not np.all(np.diff(self.lon_coords) >= 0):
            raise ValueError("经度坐标必须是递增排序的")
    
    def __call__(self, points):
        """
        执行插值
        
        Args:
            points: 查询点数组，形状为 (N, 2)，每行为 [lat, lon]
            
        Returns:
            插值结果数组
        """
        points = np.asarray(points)
        if points.ndim == 1:
            points = points.reshape(1, -1)
        
        result = np.full(points.shape[0], self.fill_value, dtype=float)
        
        for i, (lat, lon) in enumerate(points):
            try:
                result[i] = self._interpolate_single_point(lat, lon)
            except (IndexError, ValueError):
                result[i] = self.fill_value
        
        return result
    
    def _interpolate_single_point(self, lat, lon):
        """
        对单个点进行双线性插值
        """
        # 检查边界
        if (lat < self.lat_coords[0] or lat > self.lat_coords[-1] or
            lon < self.lon_coords[0] or lon > self.lon_coords[-1]):
            return self.fill_value
        
        # 找到包围点的网格索引
        lat_idx = np.searchsorted(self.lat_coords, lat) - 1
        lon_idx = np.searchsorted(self.lon_coords, lon) - 1
        
        # 边界处理
        lat_idx = max(0, min(lat_idx, len(self.lat_coords) - 2))
        lon_idx = max(0, min(lon_idx, len(self.lon_coords) - 2))
        
        # 获取四个角点的坐标和值
        lat1, lat2 = self.lat_coords[lat_idx], self.lat_coords[lat_idx + 1]
        lon1, lon2 = self.lon_coords[lon_idx], self.lon_coords[lon_idx + 1]
        
        # 四个角点的值
        v11 = self.values[lat_idx, lon_idx]      # (lat1, lon1)
        v12 = self.values[lat_idx, lon_idx + 1]  # (lat1, lon2)
        v21 = self.values[lat_idx + 1, lon_idx]  # (lat2, lon1)
        v22 = self.values[lat_idx + 1, lon_idx + 1]  # (lat2, lon2)
        
        # 检查是否有NaN值
        if np.isnan([v11, v12, v21, v22]).any():
            return self.fill_value
        
        # 双线性插值计算
        if lat2 == lat1 or lon2 == lon1:
            # 避免除零错误
            return v11
        
        # 归一化坐标
        t_lat = (lat - lat1) / (lat2 - lat1)
        t_lon = (lon - lon1) / (lon2 - lon1)
        
        # 双线性插值公式
        v1 = v11 * (1 - t_lon) + v12 * t_lon  # 在lat1处的插值
        v2 = v21 * (1 - t_lon) + v22 * t_lon  # 在lat2处的插值
        result = v1 * (1 - t_lat) + v2 * t_lat  # 最终插值
        
        return result

class OceanDataLoader:
    """
    海洋要素数据加载器
    用于加载和处理海风、海浪、洋流的.nc格式数据
    """
    
    def __init__(self, data_dir: str = None):
        """
        初始化海洋数据加载器
        
        Args:
            data_dir: 数据文件目录路径
        """
        self.data_dir = data_dir or "./ocean_data"
        self.wind_data = None
        self.wave_data = None
        self.current_data = None
        
        # 插值器缓存
        self.wind_interpolator = None
        self.wave_interpolator = None
        self.current_interpolator = None
        
        # 栅格参数
        self.grid_size = 0.2  # 栅格大小 0.2 x 0.2
        self.grid_count = 4   # 4x4 栅格
        
    def load_wind_data(self, file_path: str) -> bool:
        """
        加载海风数据
        
        Args:
            file_path: 海风数据文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.wind_data = xr.open_dataset(file_path)
            print(f"海风数据加载成功: {file_path}")
            print(f"数据维度: {self.wind_data.dims}")
            print(f"数据变量: {list(self.wind_data.data_vars)}")
            return True
        except Exception as e:
            print(f"海风数据加载失败: {e}")
            return False
    
    def load_wave_data(self, file_path: str) -> bool:
        """
        加载海浪数据
        
        Args:
            file_path: 海浪数据文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.wave_data = xr.open_dataset(file_path)
            print(f"海浪数据加载成功: {file_path}")
            print(f"数据维度: {self.wave_data.dims}")
            print(f"数据变量: {list(self.wave_data.data_vars)}")
            return True
        except Exception as e:
            print(f"海浪数据加载失败: {e}")
            return False
    
    def load_current_data(self, file_path: str) -> bool:
        """
        加载洋流数据
        
        Args:
            file_path: 洋流数据文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.current_data = xr.open_dataset(file_path)
            print(f"洋流数据加载成功: {file_path}")
            print(f"数据维度: {self.current_data.dims}")
            print(f"数据变量: {list(self.current_data.data_vars)}")
            return True
        except Exception as e:
            print(f"洋流数据加载失败: {e}")
            return False
    
    def setup_interpolators(self, time_index: int = 0):
        """
        设置插值器
        
        Args:
            time_index: 时间索引
        """
        if self.wind_data is not None:
            self._setup_wind_interpolator(time_index)
        
        if self.wave_data is not None:
            self._setup_wave_interpolator(time_index)
            
        if self.current_data is not None:
            self._setup_current_interpolator(time_index)
    
    def _setup_wind_interpolator(self, time_index: int):
        """
        设置海风插值器
        """
        try:
            # 假设海风数据包含 u10, v10 (10米高度风速分量)
            data_vars = list(self.wind_data.data_vars)
            
            # 获取坐标
            lat = self.wind_data.coords['lat'].values if 'lat' in self.wind_data.coords else self.wind_data.coords['latitude'].values
            lon = self.wind_data.coords['lon'].values if 'lon' in self.wind_data.coords else self.wind_data.coords['longitude'].values
            
            # 创建插值器字典
            self.wind_interpolator = {}
            
            for var in data_vars:
                if len(self.wind_data[var].dims) >= 3:  # 包含时间维度
                    data = self.wind_data[var].isel(time=time_index).values
                else:
                    data = self.wind_data[var].values
                
                self.wind_interpolator[var] = BilinearInterpolator(
                    (lat, lon), data, fill_value=0
                )
            
            print(f"海风插值器设置完成，变量: {list(self.wind_interpolator.keys())}")
            
        except Exception as e:
            print(f"海风插值器设置失败: {e}")
    
    def _setup_wave_interpolator(self, time_index: int):
        """
        设置海浪插值器
        """
        try:
            data_vars = list(self.wave_data.data_vars)
            
            # 获取坐标
            lat = self.wave_data.coords['lat'].values if 'lat' in self.wave_data.coords else self.wave_data.coords['latitude'].values
            lon = self.wave_data.coords['lon'].values if 'lon' in self.wave_data.coords else self.wave_data.coords['longitude'].values
            
            self.wave_interpolator = {}
            
            for var in data_vars:
                if len(self.wave_data[var].dims) >= 3:  # 包含时间维度
                    data = self.wave_data[var].isel(time=time_index).values
                else:
                    data = self.wave_data[var].values
                
                self.wave_interpolator[var] = BilinearInterpolator(
                    (lat, lon), data, fill_value=0
                )
            
            print(f"海浪插值器设置完成，变量: {list(self.wave_interpolator.keys())}")
            
        except Exception as e:
            print(f"海浪插值器设置失败: {e}")
    
    def _setup_current_interpolator(self, time_index: int):
        """
        设置洋流插值器
        """
        try:
            data_vars = list(self.current_data.data_vars)
            
            # 获取坐标
            lat = self.current_data.coords['lat'].values if 'lat' in self.current_data.coords else self.current_data.coords['latitude'].values
            lon = self.current_data.coords['lon'].values if 'lon' in self.current_data.coords else self.current_data.coords['longitude'].values
            
            self.current_interpolator = {}
            
            for var in data_vars:
                if len(self.current_data[var].dims) >= 3:  # 包含时间维度
                    data = self.current_data[var].isel(time=time_index).values
                else:
                    data = self.current_data[var].values
                
                self.current_interpolator[var] = BilinearInterpolator(
                    (lat, lon), data, fill_value=0
                )
            
            print(f"洋流插值器设置完成，变量: {list(self.current_interpolator.keys())}")
            
        except Exception as e:
            print(f"洋流插值器设置失败: {e}")
    
    def extract_grid_data(self, robot_lat: float, robot_lon: float) -> Dict[str, np.ndarray]:
        """
        在机器人位置提取4x4栅格数据
        
        Args:
            robot_lat: 机器人纬度
            robot_lon: 机器人经度
            
        Returns:
            Dict: 包含各种海洋要素的栅格数据
        """
        # 生成4x4栅格的坐标
        grid_coords = self._generate_grid_coordinates(robot_lat, robot_lon)
        
        result = {}
        
        # 提取海风数据
        if self.wind_interpolator:
            result['wind'] = self._extract_data_from_interpolator(
                self.wind_interpolator, grid_coords
            )
        
        # 提取海浪数据
        if self.wave_interpolator:
            result['wave'] = self._extract_data_from_interpolator(
                self.wave_interpolator, grid_coords
            )
        
        # 提取洋流数据
        if self.current_interpolator:
            result['current'] = self._extract_data_from_interpolator(
                self.current_interpolator, grid_coords
            )
        
        return result
    
    def _generate_grid_coordinates(self, center_lat: float, center_lon: float) -> np.ndarray:
        """
        生成以机器人为中心的4x4栅格坐标
        
        Args:
            center_lat: 中心纬度
            center_lon: 中心经度
            
        Returns:
            np.ndarray: 栅格坐标数组 (16, 2)
        """
        # 计算栅格偏移量
        half_size = (self.grid_count - 1) * self.grid_size / 2
        
        # 生成栅格坐标
        lat_offsets = np.linspace(-half_size, half_size, self.grid_count)
        lon_offsets = np.linspace(-half_size, half_size, self.grid_count)
        
        # 创建网格
        lat_grid, lon_grid = np.meshgrid(
            center_lat + lat_offsets, 
            center_lon + lon_offsets, 
            indexing='ij'
        )
        
        # 展平为坐标点数组
        coords = np.column_stack([
            lat_grid.flatten(),
            lon_grid.flatten()
        ])
        
        return coords
    
    def _extract_data_from_interpolator(self, interpolator_dict: Dict, coords: np.ndarray) -> Dict[str, np.ndarray]:
        """
        从插值器提取数据
        
        Args:
            interpolator_dict: 插值器字典
            coords: 坐标数组
            
        Returns:
            Dict: 提取的数据
        """
        result = {}
        
        for var_name, interpolator in interpolator_dict.items():
            try:
                # 插值提取数据
                values = interpolator(coords)
                # 重塑为4x4网格
                result[var_name] = values.reshape(self.grid_count, self.grid_count)
            except Exception as e:
                print(f"变量 {var_name} 插值失败: {e}")
                result[var_name] = np.zeros((self.grid_count, self.grid_count))
        
        return result
    
    def get_data_summary(self) -> Dict[str, str]:
        """
        获取数据摘要信息
        
        Returns:
            Dict: 数据摘要
        """
        summary = {}
        
        if self.wind_data:
            summary['wind'] = f"维度: {self.wind_data.dims}, 变量: {list(self.wind_data.data_vars)}"
        
        if self.wave_data:
            summary['wave'] = f"维度: {self.wave_data.dims}, 变量: {list(self.wave_data.data_vars)}"
        
        if self.current_data:
            summary['current'] = f"维度: {self.current_data.dims}, 变量: {list(self.current_data.data_vars)}"
        
        return summary