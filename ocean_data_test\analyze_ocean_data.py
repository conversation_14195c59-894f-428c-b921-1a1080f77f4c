#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋数据分析脚本
用于从提取的海洋数据中进行数据分析和自定义可视化
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd
# 移除scipy依赖，使用numpy替代


class OceanDataAnalyzer:
    """
    海洋数据分析工具
    用于分析提取的海洋数据并创建自定义可视化
    """
    
    def __init__(self, data_path="extracted_ocean_data.pkl"):
        """
        初始化分析工具
        
        Args:
            data_path: 提取数据的路径
        """
        self.data_path = data_path
        self.data = None
        self.load_data()
        
    def load_data(self):
        """
        加载提取的数据
        """
        try:
            with open(self.data_path, 'rb') as f:
                self.data = pickle.load(f)
            print(f"数据已加载: {self.data_path}")
            print(f"包含数据类型: {list(self.data.keys())}")
            
            # 打印每种数据类型的基本信息
            for data_type, data_info in self.data.items():
                print(f"\n{data_type.upper()} 数据:")
                print(f"  坐标范围: 经度 {data_info['coordinates']['longitude'].min():.2f}°-{data_info['coordinates']['longitude'].max():.2f}°")
                print(f"           纬度 {data_info['coordinates']['latitude'].min():.2f}°-{data_info['coordinates']['latitude'].max():.2f}°")
                print(f"  数据分辨率: {len(data_info['coordinates']['latitude'])} x {len(data_info['coordinates']['longitude'])}")
                print(f"  变量列表: {list(data_info['variables'].keys())}")
                
        except Exception as e:
            print(f"加载数据失败: {e}")
            self.data = None
    
    def analyze_data_statistics(self, save_path="ocean_data_statistics.png"):
        """
        分析数据统计特性并绘制直方图
        
        Args:
            save_path: 保存图像的路径
        """
        if self.data is None:
            print("没有可用数据")
            return
        
        # 确定需要分析的变量
        variables_to_analyze = []
        
        # 风场数据
        if 'wind' in self.data:
            for var_name in ['u10', 'v10']:
                if var_name in self.data['wind']['variables']:
                    variables_to_analyze.append(('wind', var_name))
        
        # 浪场数据
        if 'wave' in self.data:
            for var_name in ['swh']:
                if var_name in self.data['wave']['variables']:
                    variables_to_analyze.append(('wave', var_name))
        
        # 流场数据
        if 'current' in self.data:
            for var_name in ['water_u', 'water_v', 'water_temp', 'salinity']:
                if var_name in self.data['current']['variables']:
                    variables_to_analyze.append(('current', var_name))
        
        # 创建图形
        n_vars = len(variables_to_analyze)
        if n_vars == 0:
            print("没有可分析的变量")
            return
        
        n_rows = (n_vars + 1) // 2  # 向上取整
        n_cols = min(2, n_vars)  # 最多2列
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
        if n_vars == 1:
            axes = np.array([axes])  # 确保axes是数组
        
        # 扁平化axes数组以便迭代
        if n_rows > 1 and n_cols > 1:
            axes_flat = axes.flatten()
        else:
            axes_flat = axes
        
        # 分析每个变量
        stats_data = []
        for i, (data_type, var_name) in enumerate(variables_to_analyze):
            if i < len(axes_flat):
                ax = axes_flat[i]
                
                # 获取数据
                var_data = self.data[data_type]['variables'][var_name]['data']
                
                # 处理多维数组
                if len(var_data.shape) > 2:
                    # 假设前两个维度是时间和深度，取第一个时间和深度层
                    var_data = var_data[0, 0]
                
                # 过滤NaN值
                valid_data = var_data[~np.isnan(var_data)]
                
                if len(valid_data) > 0:
                    # 计算统计量
                    min_val = np.min(valid_data)
                    max_val = np.max(valid_data)
                    mean_val = np.mean(valid_data)
                    median_val = np.median(valid_data)
                    std_val = np.std(valid_data)
                    
                    # 使用NumPy计算偏度和峰度
                    # 偏度计算：三阶中心矩除以标准差的三次方
                    skew_val = np.mean(((valid_data - mean_val) / std_val) ** 3) if std_val > 0 else 0
                    # 峰度计算：四阶中心矩除以标准差的四次方，减3使正态分布的峰度为0
                    kurtosis_val = np.mean(((valid_data - mean_val) / std_val) ** 4) - 3 if std_val > 0 else 0
                    
                    # 绘制直方图
                    n, bins, patches = ax.hist(valid_data.flatten(), bins=30, alpha=0.7, 
                                             color='skyblue', edgecolor='black')
                    
                    # 添加正态分布曲线
                    x = np.linspace(min_val, max_val, 100)
                    # 使用NumPy计算正态分布概率密度函数
                    y = (1 / (std_val * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mean_val) / std_val) ** 2) * len(valid_data) * (bins[1] - bins[0])
                    ax.plot(x, y, 'r-', linewidth=2)
                    
                    # 添加统计信息
                    stats_text = f"均值: {mean_val:.2f}\n中位数: {median_val:.2f}\n标准差: {std_val:.2f}"
                    stats_text += f"\n偏度: {skew_val:.2f}\n峰度: {kurtosis_val:.2f}"
                    ax.text(0.95, 0.95, stats_text, transform=ax.transAxes, 
                           verticalalignment='top', horizontalalignment='right',
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                    
                    # 设置标题和标签
                    ax.set_title(f"{data_type.capitalize()} - {var_name} 分布")
                    ax.set_xlabel(var_name)
                    ax.set_ylabel("频数")
                    ax.grid(True, linestyle='--', alpha=0.5)
                    
                    # 保存统计数据
                    stats_data.append({
                        'data_type': data_type,
                        'variable': var_name,
                        'min': min_val,
                        'max': max_val,
                        'mean': mean_val,
                        'median': median_val,
                        'std': std_val,
                        'skewness': skew_val,
                        'kurtosis': kurtosis_val,
                        'valid_points': len(valid_data),
                        'total_points': var_data.size,
                        'valid_rate': len(valid_data) / var_data.size * 100
                    })
                else:
                    ax.text(0.5, 0.5, f"没有有效的{var_name}数据", ha='center', va='center')
                    ax.set_title(f"{data_type.capitalize()} - {var_name}")
        
        # 隐藏多余的子图
        for i in range(len(variables_to_analyze), len(axes_flat)):
            axes_flat[i].axis('off')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"统计分析图像已保存: {save_path}")
        
        # 显示图像
        plt.show()
        
        # 创建统计数据表格
        if stats_data:
            df = pd.DataFrame(stats_data)
            print("\n数据统计摘要:")
            print(df.to_string(index=False))
            
            # 保存统计数据到CSV
            csv_path = save_path.replace('.png', '.csv')
            df.to_csv(csv_path, index=False)
            print(f"统计数据已保存: {csv_path}")
    
    def create_correlation_map(self, save_path="ocean_data_correlation.png"):
        """
        创建变量之间的相关性热力图
        
        Args:
            save_path: 保存图像的路径
        """
        if self.data is None:
            print("没有可用数据")
            return
        
        # 收集所有变量数据
        var_data = {}
        all_lengths = []
        
        # 风场数据
        if 'wind' in self.data:
            wind_data = self.data['wind']
            for var_name in ['u10', 'v10']:
                if var_name in wind_data['variables']:
                    data = wind_data['variables'][var_name]['data']
                    flattened = data.flatten()
                    var_data[f"wind_{var_name}"] = flattened
                    all_lengths.append(len(flattened))
        
        # 浪场数据
        if 'wave' in self.data:
            wave_data = self.data['wave']
            for var_name in ['swh']:
                if var_name in wave_data['variables']:
                    data = wave_data['variables'][var_name]['data']
                    flattened = data.flatten()
                    var_data[f"wave_{var_name}"] = flattened
                    all_lengths.append(len(flattened))
        
        # 流场数据
        if 'current' in self.data:
            current_data = self.data['current']
            for var_name in ['water_u', 'water_v', 'water_temp', 'salinity']:
                if var_name in current_data['variables']:
                    data = current_data['variables'][var_name]['data']
                    # 处理多维数组
                    if len(data.shape) > 2:
                        data = data[0, 0]  # 取第一个时间和深度层
                    flattened = data.flatten()
                    var_data[f"current_{var_name}"] = flattened
                    all_lengths.append(len(flattened))
        
        # 检查是否所有数组长度一致
        if not all_lengths:
            print("警告：没有可用的变量数据，无法创建相关性热力图")
            return
        
        if len(set(all_lengths)) > 1:
            print("警告：变量数组长度不一致，尝试重采样到相同大小")
            print(f"原始数组长度：{all_lengths}")
            
            # 找到最小的数组长度作为目标大小
            min_length = min(all_lengths)
            print(f"重采样到大小: {min_length}")
            
            # 重采样所有数组到相同大小
            for key in list(var_data.keys()):
                if len(var_data[key]) > min_length:
                    # 通过随机采样减少数组大小
                    indices = np.random.choice(len(var_data[key]), min_length, replace=False)
                    var_data[key] = var_data[key][indices]
            
            print(f"重采样后所有数组长度: {min_length}")

        
        # 创建数据框
        df = pd.DataFrame(var_data)
        
        # 删除包含NaN的行
        df = df.dropna()
        
        if df.empty:
            print("没有足够的有效数据进行相关性分析")
            return
        
        # 计算相关系数
        corr = df.corr()
        
        # 创建热力图
        plt.figure(figsize=(12, 10))
        cmap = plt.cm.RdBu_r
        mask = np.triu(np.ones_like(corr, dtype=bool))
        
        # 绘制热力图
        heatmap = plt.pcolor(corr, cmap=cmap, vmin=-1, vmax=1)
        plt.colorbar(heatmap)
        
        # 设置刻度标签
        plt.xticks(np.arange(0.5, len(corr.columns)), corr.columns, rotation=45, ha='right')
        plt.yticks(np.arange(0.5, len(corr.index)), corr.index)
        
        # 添加相关系数文本
        for i in range(len(corr.columns)):
            for j in range(len(corr.index)):
                if i <= j:  # 只显示下三角
                    continue
                plt.text(i + 0.5, j + 0.5, f"{corr.iloc[j, i]:.2f}",
                         ha="center", va="center", color="white" if abs(corr.iloc[j, i]) > 0.5 else "black")
        
        # 设置标题和标签
        plt.title("变量相关性热力图", fontsize=16)
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"相关性热力图已保存: {save_path}")
        
        # 显示图像
        plt.show()
        
        # 打印相关系数矩阵
        print("\n相关系数矩阵:")
        print(corr.to_string())
    
    def create_wind_wave_comparison(self, save_path="wind_wave_comparison.png"):
        """
        创建风场和浪场的比较图
        
        Args:
            save_path: 保存图像的路径
        """
        if self.data is None or 'wind' not in self.data or 'wave' not in self.data:
            print("没有足够的数据进行风浪比较")
            return
        
        wind_data = self.data['wind']
        wave_data = self.data['wave']
        
        # 检查必要的变量
        if 'u10' not in wind_data['variables'] or 'v10' not in wind_data['variables'] or 'swh' not in wave_data['variables']:
            print("缺少必要的风场或浪场变量")
            return
        
        # 获取风场数据
        u10 = wind_data['variables']['u10']['data']
        v10 = wind_data['variables']['v10']['data']
        wind_speed = np.sqrt(u10**2 + v10**2)
        wind_lats = wind_data['coordinates']['latitude']
        wind_lons = wind_data['coordinates']['longitude']
        
        # 获取浪场数据
        swh = wave_data['variables']['swh']['data']
        wave_lats = wave_data['coordinates']['latitude']
        wave_lons = wave_data['coordinates']['longitude']
        
        # 创建图形
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        
        # 绘制风速分布
        wind_lon_grid, wind_lat_grid = np.meshgrid(wind_lons, wind_lats)
        cf1 = ax1.contourf(wind_lon_grid, wind_lat_grid, wind_speed, cmap='YlOrRd', levels=15)
        ax1.set_title("风速分布 (m/s)")
        ax1.set_xlabel("经度 (°E)")
        ax1.set_ylabel("纬度 (°N)")
        plt.colorbar(cf1, ax=ax1)
        
        # 绘制有效波高分布
        wave_lon_grid, wave_lat_grid = np.meshgrid(wave_lons, wave_lats)
        cf2 = ax2.contourf(wave_lon_grid, wave_lat_grid, swh, cmap='Blues', levels=15)
        ax2.set_title("有效波高分布 (m)")
        ax2.set_xlabel("经度 (°E)")
        ax2.set_ylabel("纬度 (°N)")
        plt.colorbar(cf2, ax=ax2)
        
        # 创建散点图比较风速和波高
        # 需要将数据重采样到相同的网格
        # 这里简化处理，只取中心点附近的数据
        wind_center_lat_idx = len(wind_lats) // 2
        wind_center_lon_idx = len(wind_lons) // 2
        wave_center_lat_idx = len(wave_lats) // 2
        wave_center_lon_idx = len(wave_lons) // 2
        
        # 提取中心区域数据
        radius = min(10, wind_center_lat_idx, wind_center_lon_idx, wave_center_lat_idx, wave_center_lon_idx)
        
        wind_subset = wind_speed[
            wind_center_lat_idx-radius:wind_center_lat_idx+radius,
            wind_center_lon_idx-radius:wind_center_lon_idx+radius
        ].flatten()
        
        wave_subset = swh[
            wave_center_lat_idx-radius:wave_center_lat_idx+radius,
            wave_center_lon_idx-radius:wave_center_lon_idx+radius
        ].flatten()
        
        # 过滤NaN值
        valid_indices = ~(np.isnan(wind_subset) | np.isnan(wave_subset))
        wind_subset = wind_subset[valid_indices]
        wave_subset = wave_subset[valid_indices]
        
        if len(wind_subset) > 0 and len(wave_subset) > 0:
            # 绘制散点图
            ax3.scatter(wind_subset, wave_subset, alpha=0.5)
            ax3.set_xlabel("风速 (m/s)")
            ax3.set_ylabel("有效波高 (m)")
            ax3.set_title("风速与波高关系")
            ax3.grid(True)
            
            # 添加趋势线
            if len(wind_subset) > 1:
                z = np.polyfit(wind_subset, wave_subset, 1)
                p = np.poly1d(z)
                ax3.plot(wind_subset, p(wind_subset), "r--", linewidth=2)
                
                # 计算相关系数
                corr = np.corrcoef(wind_subset, wave_subset)[0, 1]
                ax3.text(0.05, 0.95, f"相关系数: {corr:.2f}\ny = {z[0]:.4f}x + {z[1]:.4f}", 
                        transform=ax3.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        else:
            ax3.text(0.5, 0.5, "没有足够的有效数据进行比较", ha='center', va='center')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"风浪比较图已保存: {save_path}")
        
        # 显示图像
        plt.show()


def main():
    """
    主函数
    """
    print("海洋数据分析工具")
    print("=" * 50)
    
    # 创建分析器
    analyzer = OceanDataAnalyzer("extracted_ocean_data.pkl")
    
    # 分析数据统计特性
    analyzer.analyze_data_statistics("ocean_data_statistics.png")
    
    # 创建相关性热力图
    analyzer.create_correlation_map("ocean_data_correlation.png")
    
    # 创建风浪比较图
    analyzer.create_wind_wave_comparison("wind_wave_comparison.png")


if __name__ == "__main__":
    main()