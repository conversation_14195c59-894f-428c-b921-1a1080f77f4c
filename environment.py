import gymnasium as gym
import numpy as np
from gymnasium import spaces
import matplotlib.pyplot as plt
import math
import os
from configs.obstacle_configs import get_obstacle_config
from coordinate_transform import global_to_local, normalize_angle, get_local_velocity
from ocean_data.ocean_data_extractor import OceanDataExtractor
class PathPlanningEnv(gym.Env):
    def __init__(self, map_size=100, num_obstacles=24, robot_radius=0.5, render_mode=None, use_ocean_data=False, ocean_data_path=None, ocean_grid_size=4, ocean_step_size=0.2):
        super(PathPlanningEnv, self).__init__()
        
        self.map_size = map_size
        self.num_obstacles = num_obstacles
        self.robot_radius = robot_radius
        self.render_mode = render_mode
        self.use_ocean_data = use_ocean_data
        
        # 动作空间：连续的线速度和角速度
        self.action_space = spaces.Box(
            low=np.array([-1.0, -1.0]), 
            high=np.array([1.0, 1.0]), 
            dtype=np.float32
        )
        
        # 海洋数据参数
        self.ocean_data_path = ocean_data_path if ocean_data_path else os.path.join("ocean_data", "extracted_ocean_data.pkl")
        self.ocean_data_extractor = None
        self.ocean_data = None
        self.ocean_grid_size = ocean_grid_size  # 默认4x4网格
        self.ocean_grid_step = ocean_step_size  # 默认0.2的步长
        self.ocean_variables = ["u10", "v10", "swh", "water_u", "water_v"]  # 要使用的海洋变量
        
        # 加载海洋数据
        if self.use_ocean_data:
            self._load_ocean_data()
        
        # 状态空间：36维雷达 + 机器人位置(2) + 目标位置(2) + 速度(2) + 距离和角度(2) + 海洋数据(4x4x5)
        ocean_data_dim = self.ocean_grid_size * self.ocean_grid_size * len(self.ocean_variables) if self.use_ocean_data else 0
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=(44 + ocean_data_dim,), dtype=np.float32
        )
        
        # 雷达参数
        self.radar_range = 5.0
        self.radar_angles = np.linspace(-np.pi, np.pi, 36, endpoint=False)
        
        # 固定障碍物配置（保证训练一致性）
        self.obstacles = self._generate_fixed_obstacles()
        
        # 运动参数
        self.max_linear_vel = 2.0
        self.max_angular_vel = 2.0
        self.dt = 0.1
        
        # 任务参数
        self.success_threshold = 1.0
        self.max_steps = 1200
        
        self.reset()
    
    def _generate_fixed_obstacles(self):
        """从配置文件加载障碍物配置"""
        obstacles = []
        # 从配置文件获取障碍物配置
        obstacle_configs = get_obstacle_config(self.num_obstacles)
        
        for x, y, r in obstacle_configs:
            obstacles.append({
                'x': x, 'y': y, 'radius': r
            })
        
        return obstacles
    
    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        
        # 随机生成起点和终点，确保不在障碍物内
        self.start_pos = self._get_valid_position()
        self.target_pos = self._get_valid_position()
        
        # 确保起点和终点有足够距离
        while np.linalg.norm(np.array(self.start_pos) - np.array(self.target_pos)) < 10:
            self.target_pos = self._get_valid_position()
        
        # 初始化机器人状态
        self.robot_pos = np.array(self.start_pos, dtype=np.float32)
        self.robot_vel = np.array([0.0, 0.0], dtype=np.float32)
        self.robot_angle = np.random.uniform(-np.pi, np.pi)
        
        self.step_count = 0
        self.prev_distance = self._get_distance_to_target()
        
        # 初始化雷达读数
        self._last_radar_readings = self._get_radar_readings()
        
        return self._get_observation(), {}
    
    def _get_valid_position(self):
        """获取不与障碍物碰撞且保持安全距离的有效位置"""
        min_safe_distance = 2.0  # 最小安全距离2米
        max_attempts = 1000  # 最大尝试次数，避免无限循环
        
        for attempt in range(max_attempts):
            pos = [
                np.random.uniform(5, self.map_size - 5),
                np.random.uniform(5, self.map_size - 5)
            ]
            
            # 检查与所有障碍物的距离
            safe_position = True
            for obstacle in self.obstacles:
                distance = np.sqrt((pos[0] - obstacle['x'])**2 + (pos[1] - obstacle['y'])**2)
                # 确保距离大于障碍物半径 + 机器人半径 + 安全距离
                required_distance = obstacle['radius'] + self.robot_radius + min_safe_distance
                if distance < required_distance:
                    safe_position = False
                    break
            
            if safe_position:
                return pos
        
        # 如果尝试次数过多仍未找到合适位置，降低安全距离要求
        print(f"警告：经过{max_attempts}次尝试未找到满足2米安全距离的位置，降低安全距离要求")
        return self._get_fallback_position()
    
    def _get_fallback_position(self):
        """备用位置生成方法，使用较低的安全距离要求"""
        min_safe_distance = 1.0  # 降低到1米安全距离
        max_attempts = 500
        
        for attempt in range(max_attempts):
            pos = [
                np.random.uniform(5, self.map_size - 5),
                np.random.uniform(5, self.map_size - 5)
            ]
            
            # 检查与所有障碍物的距离
            safe_position = True
            for obstacle in self.obstacles:
                distance = np.sqrt((pos[0] - obstacle['x'])**2 + (pos[1] - obstacle['y'])**2)
                # 确保距离大于障碍物半径 + 机器人半径 + 降低的安全距离
                required_distance = obstacle['radius'] + self.robot_radius + min_safe_distance
                if distance < required_distance:
                    safe_position = False
                    break
            
            if safe_position:
                return pos
        
        # 最后的备用方案：只确保不碰撞
        print("警告：使用最基本的碰撞检测生成位置")
        while True:
            pos = [
                np.random.uniform(10, self.map_size - 10),
                np.random.uniform(10, self.map_size - 10)
            ]
            
            collision, _ = self._check_collision(pos)
            if not collision:
                return pos
    
    def _check_collision(self, pos=None):
        """使用雷达检测的距离进行碰撞判断"""
        # 保存原始位置（如果需要）
        original_pos = None
        temp_pos_set = False
        
        if pos is not None:
            if hasattr(self, 'robot_pos'):
                original_pos = self.robot_pos.copy()
            else:
                temp_pos_set = True
            # 临时设置位置用于检测
            self.robot_pos = np.array(pos)
        
        # 确保robot_angle已初始化
        if not hasattr(self, 'robot_angle'):
            self.robot_angle = 0.0
            
        # 获取雷达读数
        radar_readings = self._get_radar_readings()
        
        # 恢复原始位置（如果有）或清理临时位置
        if original_pos is not None:
            self.robot_pos = original_pos
        elif temp_pos_set:
            # 如果是临时设置的位置，删除它
            delattr(self, 'robot_pos')
        
        # 如果任何雷达读数小于机器人半径，则认为发生碰撞
        if np.min(radar_readings) <= self.robot_radius * 1.2:  # 添加一点余量
            return True, radar_readings
        return False, radar_readings

    def step(self, action):
        # 动作限制和缩放
        action = np.clip(action, -1.0, 1.0)
        linear_vel = action[0] * self.max_linear_vel # max_linear_vel=2.0
        angular_vel = action[1] * self.max_angular_vel # max_angular_vel=2.0
        
        # 更新机器人状态
        self._update_robot_state(linear_vel, angular_vel)
        
        # 碰撞检查和位置更新
        collision, radar_readings = self._handle_collision_and_movement()
        
        # 保存雷达读数供观测使用
        self._last_radar_readings = radar_readings
        
        # 计算当前距离（避免重复计算）
        current_distance = self._get_distance_to_target()
        
        # 计算奖励
        reward = self._calculate_reward(action, radar_readings, current_distance)
        
        # 检查终止条件
        terminated = self._check_termination_conditions(collision, current_distance)
        
        # 更新状态
        self.prev_distance = current_distance
        self.step_count += 1
        
        # 超时检查
        if self.step_count >= self.max_steps:
            terminated = True
        
        truncated = False
        info = {
            'distance_to_target': current_distance,
            'collision': collision,
            'success': self._is_goal_reached(current_distance)
        }
        
        return self._get_observation(), reward, terminated, truncated, info
    
    def render(self):
        if self.render_mode is None:
            return
            
        plt.figure(figsize=(8, 8))
        plt.xlim(0, self.map_size)
        plt.ylim(0, self.map_size)
        
        # 绘制障碍物
        for obs in self.obstacles:
            circle = plt.Circle((obs['x'], obs['y']), obs['radius'], color='red', alpha=0.7)
            plt.gca().add_patch(circle)
        
        # 绘制机器人
        robot_circle = plt.Circle(self.robot_pos, self.robot_radius, color='blue', alpha=0.7)
        plt.gca().add_patch(robot_circle)
        
        # 绘制机器人朝向
        arrow_length = 5
        arrow_end = self.robot_pos + arrow_length * np.array([np.cos(self.robot_angle), np.sin(self.robot_angle)])
        plt.arrow(self.robot_pos[0], self.robot_pos[1], 
                 arrow_end[0] - self.robot_pos[0], arrow_end[1] - self.robot_pos[1],
                 head_width=1, head_length=1, fc='blue', ec='blue')
        
        # 绘制目标
        target_circle = plt.Circle(self.target_pos, self.success_threshold, 
                                 color='green', alpha=0.3)
        plt.gca().add_patch(target_circle)
        plt.plot(self.target_pos[0], self.target_pos[1], 'g*', markersize=15)
        
        # 绘制起点
        plt.plot(self.start_pos[0], self.start_pos[1], 'bo', markersize=8)
        
        plt.title(f'Step: {self.step_count}, Distance: {np.linalg.norm(self.robot_pos - np.array(self.target_pos)):.2f}')
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.show()
    
    def close(self):
        plt.close('all')

    def _get_radar_readings(self):
        """获取36维雷达读数（矩阵优化版本）"""
        # 预计算所有角度
        angles = self.radar_angles + self.robot_angle
        cos_angles = np.cos(angles)
        sin_angles = np.sin(angles)
        
        # 创建检测步长矩阵，确保最大距离不超过radar_range
        detection_steps = 20
        step_distances = np.linspace(0, self.radar_range, detection_steps)
        
        # 创建射线位置矩阵 [radar_angles数量, detection_steps, 2]
        # 使用广播机制一次性计算所有射线位置
        cos_matrix = cos_angles[:, np.newaxis]  # [radar_angles数量, 1]
        sin_matrix = sin_angles[:, np.newaxis]  # [radar_angles数量, 1]
        distances_matrix = step_distances[np.newaxis, :]  # [1, detection_steps]
        
        # 计算所有射线点的坐标 [radar_angles数量, detection_steps]
        ray_x = self.robot_pos[0] + distances_matrix * cos_matrix
        ray_y = self.robot_pos[1] + distances_matrix * sin_matrix
        
        # 边界检测矩阵
        boundary_mask = ((ray_x <= 0.2) | (ray_x >= self.map_size - 0.2) | 
                        (ray_y <= 0.2) | (ray_y >= self.map_size - 0.2))
        
        # 障碍物检测矩阵
        obs_centers = np.array([[obs['x'], obs['y']] for obs in self.obstacles])  # [n_obstacles, 2]
        obs_radii_sq = np.array([obs['radius']**2 for obs in self.obstacles])  # [n_obstacles]
        
        # 计算所有射线点到所有障碍物的距离平方
        # ray_positions: [radar_angles数量, detection_steps, 2]
        ray_positions = np.stack([ray_x, ray_y], axis=-1)
        
        # 使用广播计算距离平方 [radar_angles数量, detection_steps, n_obstacles]
        diff = ray_positions[:, :, np.newaxis, :] - obs_centers[np.newaxis, np.newaxis, :, :]
        distances_sq = np.sum(diff**2, axis=-1)
        
        # 检查碰撞 [radar_angles数量, detection_steps]
        obstacle_mask = np.any(distances_sq <= obs_radii_sq[np.newaxis, np.newaxis, :], axis=-1)
        
        # 合并边界和障碍物掩码
        collision_mask = boundary_mask | obstacle_mask
        
        # 找到每个雷达角度的第一个碰撞点，如果没有碰撞则返回最大雷达距离
        radar_readings = np.full(len(self.radar_angles), self.radar_range)  # 默认为最大雷达距离
        
        for i in range(len(self.radar_angles)):
            collision_indices = np.where(collision_mask[i])[0]
            if len(collision_indices) > 0:
                first_collision_step = collision_indices[0]
                # 确保检测距离不超过最大雷达范围
                radar_readings[i] = step_distances[first_collision_step]
        
        # 确保所有雷达数据都不超过最大范围
        radar_readings = np.clip(radar_readings, 0.0, self.radar_range)
        
        return np.array(radar_readings, dtype=np.float32)
    
    def _load_ocean_data(self):
        """加载海洋数据"""
        try:
            self.ocean_data_extractor = OceanDataExtractor(self.ocean_data_path)
            if self.ocean_data_extractor.load_data():
                self.ocean_data = self.ocean_data_extractor.extract_variables()
                print("海洋数据加载成功")
                
                # 打印海洋数据的基本信息
                for var_name in self.ocean_variables:
                    for data_type in self.ocean_data:
                        if 'variables' in self.ocean_data[data_type] and var_name in self.ocean_data[data_type]['variables']:
                            var_data = self.ocean_data[data_type]['variables'][var_name]
                            if isinstance(var_data, np.ndarray):
                                print(f"变量 {var_name} 形状: {var_data.shape}")
            else:
                print(f"警告: 无法加载海洋数据，将不使用海洋数据")
                self.use_ocean_data = False
        except Exception as e:
            print(f"加载海洋数据时出错: {e}")
            self.use_ocean_data = False
    
    def _get_ocean_data_grid(self):
        """获取当前位置周围的海洋数据网格"""
        if not self.use_ocean_data or self.ocean_data is None:
            # 如果不使用海洋数据或数据加载失败，返回全零数组
            return np.zeros((self.ocean_grid_size, self.ocean_grid_size, len(self.ocean_variables)), dtype=np.float32)
        
        # 获取当前位置
        lat, lon = self.robot_pos  # 假设机器人位置可以直接映射到经纬度
        
        # 计算网格的起始位置
        start_lat = lat - (self.ocean_grid_size // 2) * self.ocean_grid_step
        start_lon = lon - (self.ocean_grid_size // 2) * self.ocean_grid_step
        
        # 创建网格数据数组
        grid_data = np.zeros((self.ocean_grid_size, self.ocean_grid_size, len(self.ocean_variables)), dtype=np.float32)
        
        # 填充网格数据
        for i in range(self.ocean_grid_size):
            for j in range(self.ocean_grid_size):
                # 计算当前网格点的经纬度
                grid_lat = start_lat + i * self.ocean_grid_step
                grid_lon = start_lon + j * self.ocean_grid_step
                
                # 获取每个变量在该位置的值
                for k, var_name in enumerate(self.ocean_variables):
                    try:
                        value = self.ocean_data_extractor.get_value_at_position(var_name, grid_lat, grid_lon)
                        if value is not None and not np.isnan(value):
                            grid_data[i, j, k] = value
                    except Exception as e:
                        # 如果获取失败，保持为0
                        pass
        
        # 对数据进行归一化处理
        # 这里使用简单的最大值归一化，实际应用中可能需要更复杂的归一化方法
        for k in range(len(self.ocean_variables)):
            max_val = np.max(np.abs(grid_data[:, :, k]))
            if max_val > 0:
                grid_data[:, :, k] = grid_data[:, :, k] / max_val
        
        return grid_data
    
    def _load_ocean_data(self):
        """加载海洋数据"""
        try:
            print(f"正在加载海洋数据: {self.ocean_data_path}")
            self.ocean_data_extractor = OceanDataExtractor(self.ocean_data_path)
            
            # 验证所有需要的变量是否存在
            for var in self.ocean_variables:
                if not self.ocean_data_extractor.has_variable(var):
                    print(f"警告: 海洋数据中缺少变量 {var}")
                    self.use_ocean_data = False
                    return
            
            # 获取海洋数据的坐标范围
            lat_range, lon_range = self.ocean_data_extractor.get_coordinate_range()
            print(f"海洋数据坐标范围: 纬度 {lat_range}, 经度 {lon_range}")
            
            self.ocean_data = {}
            for var in self.ocean_variables:
                self.ocean_data[var] = self.ocean_data_extractor.get_variable_data(var)
                if self.ocean_data[var] is None:
                    print(f"警告: 无法加载变量 {var}")
                    self.use_ocean_data = False
                    return
                
            print("海洋数据加载成功")
        except Exception as e:
            print(f"加载海洋数据时出错: {e}")
            self.use_ocean_data = False
    
    def _get_ocean_data_grid(self):
        """获取当前位置周围的海洋数据网格"""
        if not self.use_ocean_data or self.ocean_data_extractor is None:
            # 如果未启用海洋数据或加载失败，返回全零数组
            return np.zeros((self.ocean_grid_size, self.ocean_grid_size, len(self.ocean_variables)))
        
        try:
            # 创建网格数组
            grid = np.zeros((self.ocean_grid_size, self.ocean_grid_size, len(self.ocean_variables)))
            
            # 计算网格的起始位置（以机器人为中心）
            start_x = self.robot_pos[0] - (self.ocean_grid_size // 2) * self.ocean_grid_step
            start_y = self.robot_pos[1] - (self.ocean_grid_size // 2) * self.ocean_grid_step
            
            # 填充网格
            for i in range(self.ocean_grid_size):
                for j in range(self.ocean_grid_size):
                    # 计算当前网格点的全局坐标
                    x = start_x + i * self.ocean_grid_step
                    y = start_y + j * self.ocean_grid_step
                    
                    # 获取每个变量在该位置的值
                    for k, var in enumerate(self.ocean_variables):
                        try:
                            value = self.ocean_data_extractor.get_value_at_position(var, x, y)
                            # 处理可能的NaN值
                            if np.isnan(value):
                                value = 0.0
                            grid[i, j, k] = value
                        except Exception as e:
                            # 出错时使用0值
                            grid[i, j, k] = 0.0
            
            # 对每个变量进行归一化处理（使用最大值归一化）
            for k in range(len(self.ocean_variables)):
                var_data = grid[:, :, k]
                max_val = np.max(np.abs(var_data))
                if max_val > 0:
                    grid[:, :, k] = var_data / max_val
            
            return grid
        except Exception as e:
            print(f"获取海洋数据网格时出错: {e}")
            return np.zeros((self.ocean_grid_size, self.ocean_grid_size, len(self.ocean_variables)))
    
    def _get_observation(self):
        """获取完整观测（局部坐标系）"""
        
        # 使用已保存的雷达数据，如果没有则重新获取
        radar_data = self._get_cached_radar_data()
        
        # 将目标位置转换到局部坐标系
        target_local = global_to_local(self.target_pos, self.robot_pos, self.robot_angle)
        
        # 局部坐标系下的速度
        local_vel = get_local_velocity(self.robot_vel, self.robot_angle)
        
        # 机器人状态（局部坐标系）
        robot_state = self._get_normalized_robot_state(target_local, local_vel)
        
        # 相对目标信息
        relative_info = self._get_relative_target_info(target_local)
        
        # 归一化雷达数据
        radar_normalized = radar_data / self.radar_range
        
        # 基本观测
        basic_obs = np.concatenate([radar_normalized, robot_state, relative_info])
        
        # 如果使用海洋数据，添加到观测中
        if self.use_ocean_data and self.ocean_data_extractor is not None:
            # 获取海洋数据网格
            ocean_grid = self._get_ocean_data_grid()
            # 将网格展平为一维数组
            ocean_flat = ocean_grid.flatten()
            # 合并基本观测和海洋数据
            return np.concatenate([basic_obs, ocean_flat])
        else:
            return basic_obs
    
    def _calculate_reward(self, action, radar_data=None, distance_to_goal=None):
        """简化的奖励函数"""
        # 获取必要数据
        if distance_to_goal is None:
            distance_to_goal = self._get_distance_to_target()
        if radar_data is None:
            radar_data = self._get_radar_readings()
        
        # 终止状态奖励
        if self._is_goal_reached(distance_to_goal):
            return 100.0
        if self._check_collision_by_radar_data(radar_data):
            return -50.0
        
        # 距离奖励：鼓励接近目标
        normalized_distance = distance_to_goal / self._get_map_diagonal()
        distance_reward = -normalized_distance
        
        # 避障奖励：基于最近障碍物距离
        min_distance = np.min(radar_data)
        safe_threshold = 3.0 * self.robot_radius
        if min_distance < safe_threshold:
            obstacle_reward = -2.0 * (safe_threshold - min_distance) / safe_threshold
        else:
            obstacle_reward = 0.0
        
        # 运动奖励：鼓励向目标移动
        target_local = global_to_local(self.target_pos, self.robot_pos, self.robot_angle)
        local_vel = get_local_velocity(self.robot_vel, self.robot_angle)
        goal_direction = target_local / (np.linalg.norm(target_local) + 1e-6)
        movement_reward = np.dot(local_vel, goal_direction) * 0.5
        
        # 动作惩罚：避免过度转向和加速
        action_penalty = -0.01 * (action[0]**2 + action[1]**2)
        
        # 时间惩罚：鼓励快速完成任务
        time_penalty = -0.01
        
        return distance_reward + obstacle_reward + movement_reward + action_penalty + time_penalty
    
    def _is_goal_reached(self, distance_to_goal=None):
        """检查是否到达目标"""
        if distance_to_goal is None:
            distance_to_goal = self._get_distance_to_target()
        return distance_to_goal < self.success_threshold
    
    def _check_collision_by_radar_data(self, radar_data):
        """基于雷达数据检查碰撞"""
        # 如果任何雷达读数小于机器人半径，则认为发生碰撞
        return np.any(radar_data < self.robot_radius)
    
    def _check_collision_by_radar(self):
        """获取雷达数据并检查碰撞"""
        radar_data = self._get_radar_readings()
        return self._check_collision_by_radar_data(radar_data)
    
    def _get_distance_to_target(self):
        """计算到目标的距离"""
        return np.linalg.norm(self.robot_pos - np.array(self.target_pos))
    
    def _get_map_diagonal(self):
        """获取地图对角线长度"""
        return np.sqrt(self.map_size**2 + self.map_size**2)
    
    def _update_robot_state(self, linear_vel, angular_vel):
        """更新机器人状态（角度、速度、位置）"""
        # 更新机器人角度
        self.robot_angle += angular_vel * self.dt
        self.robot_angle = np.arctan2(np.sin(self.robot_angle), np.cos(self.robot_angle))  # 归一化到[-π,π]
        
        # 计算机器人速度（全局坐标系）
        self.robot_vel = np.array([
            linear_vel * np.cos(self.robot_angle),
            linear_vel * np.sin(self.robot_angle)
        ])
    
    def _handle_collision_and_movement(self):
        """处理机器人移动和碰撞检测"""
        # 更新机器人位置
        new_pos = self.robot_pos + self.robot_vel * self.dt
        
        # 边界检查
        new_pos[0] = np.clip(new_pos[0], self.robot_radius, self.map_size - self.robot_radius)
        new_pos[1] = np.clip(new_pos[1], self.robot_radius, self.map_size - self.robot_radius)
        
        # 暂时更新位置以进行碰撞检查
        old_pos = self.robot_pos.copy()
        self.robot_pos = new_pos
        
        # 碰撞检查
        collision, radar_readings = self._check_collision()
        
        # 如果发生碰撞，恢复原位置
        if collision:
            self.robot_pos = old_pos
        
        return collision, radar_readings
    
    def _check_termination_conditions(self, collision, current_distance):
        """检查终止条件"""
        # 检查是否到达目标
        if self._is_goal_reached(current_distance):
            return True
        
        # 检查是否发生碰撞
        if collision:
            return True
        
        return False
    
    def _get_cached_radar_data(self):
        """获取缓存的雷达数据，如果没有则重新获取"""
        if hasattr(self, '_last_radar_readings'):
            return self._last_radar_readings
        else:
            return self._get_radar_readings()
    
    def _get_normalized_robot_state(self, target_local, local_vel):
        """获取归一化的机器人状态"""
        return np.array([
            0.0,  # 局部坐标系中机器人位置始终为原点
            0.0,
            target_local[0],  # 归一化的目标位置（局部坐标系）
            target_local[1],
            local_vel[0],  # 归一化的局部速度
            local_vel[1],
        ], dtype=np.float32)
    
    def _get_relative_target_info(self, target_local):
        """获取相对目标信息"""
        distance = np.linalg.norm(target_local)   # 归一化距离
        angle_to_target = np.arctan2(target_local[1], target_local[0])  # 局部坐标系中目标的角度
        return np.array([distance, angle_to_target], dtype=np.float32)