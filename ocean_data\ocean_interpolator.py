#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋数据快速插值器
用于船舶运动时快速获取位置的海洋气象要素
支持u10、v10、swh、water_u和water_v变量的插值
"""

import numpy as np
import pickle
import os
import logging
from typing import Dict, Tuple, Optional, List
import time

# 禁用所有日志记录器
logging.disable(logging.CRITICAL)

# 全局变量，用于控制输出
_output_enabled = False  # 默认禁用输出

def enable_output():
    global _output_enabled
    _output_enabled = True
    
def disable_output():
    global _output_enabled
    _output_enabled = False

class OceanInterpolator:
    """
    海洋数据快速插值器
    预先加载数据并构建插值函数，以便在船舶运动时快速获取位置的海洋气象要素
    使用纯NumPy实现双线性插值，避免SciPy依赖
    """
    
    # 单例模式
    _instance = None
    
    def __new__(cls, data_path=None, verbose=False):
        if cls._instance is None:
            cls._instance = super(OceanInterpolator, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, data_path: str = "extracted_ocean_data.pkl", verbose: bool = False):
        """
        初始化插值器
        
        Args:
            data_path: pickle数据文件路径
            verbose: 是否输出详细信息
        """
        # 避免重复初始化
        if getattr(self, '_initialized', False):
            return
            
        self.verbose = verbose
        self.data_path = data_path
        self.data = None
        self.coordinates = {}
        self.variable_shapes = {}
        self.variable_ranges = {}
        self.variable_data = {}
        
        # 支持的变量列表
        self.supported_variables = {
            'wind': ['u10', 'v10'],
            'wave': ['swh'],
            'current': ['water_u', 'water_v']
        }
        
        # 变量的物理有效范围
        self.variable_valid_ranges = {
            'u10': (-50.0, 50.0),      # 风速u分量，单位m/s
            'v10': (-50.0, 50.0),      # 风速v分量，单位m/s
            'swh': (0.0, 20.0),        # 有效波高，单位m
            'water_u': (-10.0, 10.0),  # 洋流u分量，单位m/s
            'water_v': (-10.0, 10.0)   # 洋流v分量，单位m/s
        }
        
        # 加载数据并准备插值
        self.load_data()
        self.prepare_interpolation()
        
        # 标记为已初始化
        self._initialized = True
    
    def log(self, message: str, level: str = "info") -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        if not self.verbose or not _output_enabled:
            return
            
        print(f"{level.upper()}: {message}")
    
    def load_data(self) -> bool:
        """
        加载pickle格式的海洋数据
        
        Returns:
            bool: 是否加载成功
        """
        if not os.path.exists(self.data_path):
            self.log(f"数据文件不存在: {self.data_path}", "error")
            return False
        
        try:
            start_time = time.time()
            with open(self.data_path, 'rb') as f:
                self.data = pickle.load(f)
            load_time = time.time() - start_time
            
            self.log(f"从 {self.data_path} 加载数据成功，耗时 {load_time:.3f} 秒")
            
            # 提取坐标信息
            for data_type in self.data:
                if 'coordinates' in self.data[data_type]:
                    self.coordinates[data_type] = {
                        'latitude': self.data[data_type]['coordinates']['latitude'],
                        'longitude': self.data[data_type]['coordinates']['longitude']
                    }
            
            return True
            
        except Exception as e:
            self.log(f"加载数据失败: {e}", "error")
            return False
    
    def prepare_interpolation(self) -> None:
        """
        准备插值所需的数据
        """
        if self.data is None:
            self.log("请先加载数据", "error")
            return
        
        start_time = time.time()
        prepared_count = 0
        
        for data_type, var_names in self.supported_variables.items():
            if data_type not in self.data:
                self.log(f"数据中不包含 {data_type} 类型", "warning")
                continue
            
            # 获取坐标
            if data_type not in self.coordinates:
                self.log(f"未找到 {data_type} 的坐标数据", "warning")
                continue
                
            lats = self.coordinates[data_type]['latitude']
            lons = self.coordinates[data_type]['longitude']
            
            # 为每个变量准备数据
            for var_name in var_names:
                if 'variables' not in self.data[data_type] or var_name not in self.data[data_type]['variables']:
                    self.log(f"未找到 {data_type}/{var_name} 变量数据", "warning")
                    continue
                
                var_data = self.data[data_type]['variables'][var_name]
                
                # 处理不同的数据结构
                if isinstance(var_data, dict) and 'data' in var_data:
                    data_array = var_data['data']
                else:
                    data_array = var_data
                
                # 处理多维数组，确保是2D数组
                original_shape = data_array.shape
                self.variable_shapes[var_name] = original_shape
                
                if len(original_shape) > 2:
                    # 对于4D数组 (time, level, lat, lon)，取第一个时间和第一个层级
                    if len(original_shape) == 4:
                        data_array = data_array[0, 0, :, :]
                    # 对于3D数组 (time, lat, lon) 或 (level, lat, lon)，取第一个时间或层级
                    elif len(original_shape) == 3:
                        data_array = data_array[0, :, :]
                
                # 确保数据维度与坐标维度匹配
                if data_array.shape != (len(lats), len(lons)):
                    # 如果维度不匹配但接近，可能是因为网格定义方式不同，尝试调整
                    if abs(data_array.shape[0] - len(lats)) <= 1 and abs(data_array.shape[1] - len(lons)) <= 1:
                        # 创建新的坐标网格，与数据维度匹配
                        lat_min, lat_max = lats.min(), lats.max()
                        lon_min, lon_max = lons.min(), lons.max()
                        new_lats = np.linspace(lat_min, lat_max, data_array.shape[0])
                        new_lons = np.linspace(lon_min, lon_max, data_array.shape[1])
                        lats, lons = new_lats, new_lons
                    else:
                        self.log(f"警告: {var_name} 数据维度 {data_array.shape} 与坐标维度 ({len(lats)}, {len(lons)}) 不匹配，无法准备插值", "warning")
                        continue
                
                # 记录数据范围
                if np.issubdtype(data_array.dtype, np.number):
                    self.variable_ranges[var_name] = {
                        'min': float(np.nanmin(data_array)),
                        'max': float(np.nanmax(data_array)),
                        'mean': float(np.nanmean(data_array))
                    }
                    
                    # 特别处理swh变量，确保数据正确
                    if var_name == 'swh':
                        # 检查是否有负值
                        neg_mask = data_array < 0
                        if np.any(neg_mask):
                            neg_count = np.sum(neg_mask)
                            self.log(f"swh有 {neg_count} 个负值，将替换为0", "warning")
                            data_array = np.copy(data_array)
                            data_array[neg_mask] = 0.0
                
                # 检查数据是否在有效范围内
                if var_name in self.variable_valid_ranges:
                    valid_min, valid_max = self.variable_valid_ranges[var_name]
                    # 将超出有效范围的值替换为NaN
                    invalid_mask = (data_array < valid_min) | (data_array > valid_max)
                    if np.any(invalid_mask):
                        invalid_count = np.sum(invalid_mask)
                        total_count = data_array.size
                        invalid_percent = invalid_count / total_count * 100
                        self.log(f"变量 {var_name} 有 {invalid_count} 个值 ({invalid_percent:.2f}%) 超出有效范围 [{valid_min}, {valid_max}]，将替换为NaN", "warning")
                        data_array = np.copy(data_array)  # 创建副本以避免修改原始数据
                        data_array[invalid_mask] = np.nan
                
                # 记录NaN值信息，但不进行填充
                if np.any(np.isnan(data_array)):
                    nan_mask = np.isnan(data_array)
                    nan_count = np.sum(nan_mask)
                    total_count = data_array.size
                    nan_percent = nan_count / total_count * 100
                    self.log(f"变量 {var_name} 有 {nan_count} 个NaN值 ({nan_percent:.2f}%)，保留这些NaN值用于碰撞检测", "info")
                
                # 存储数据和坐标
                self.variable_data[var_name] = {
                    'data': data_array,
                    'lats': lats,
                    'lons': lons,
                    'lat_range': (float(lats.min()), float(lats.max())),
                    'lon_range': (float(lons.min()), float(lons.max())),
                    'lat_ascending': np.all(np.diff(lats) > 0)  # 记录纬度是否递增
                }
                
                prepared_count += 1
                self.log(f"✓ 成功准备 {var_name} 插值数据")
        
        prep_time = time.time() - start_time
        self.log(f"准备了 {prepared_count} 个变量的插值数据，耗时 {prep_time:.3f} 秒")
    
    def bilinear_interpolate(self, data: np.ndarray, lats: np.ndarray, lons: np.ndarray, 
                            lat: float, lon: float, lat_ascending: bool = True) -> float:
        """
        使用双线性插值计算指定位置的值
        如果四个插值点中有任何一个是NaN，则返回NaN
        
        Args:
            data: 2D数据数组
            lats: 纬度坐标数组
            lons: 经度坐标数组
            lat: 目标纬度
            lon: 目标经度
            lat_ascending: 纬度是否递增排序
            
        Returns:
            float: 插值结果
        """
        # 确保坐标在有效范围内
        lat = max(min(lat, lats.max()), lats.min())
        lon = max(min(lon, lons.max()), lons.min())
        
        # 找到最近的网格点索引
        # 根据纬度排序方向选择合适的方法找到索引
        if lat_ascending:
            # 纬度递增排序
            lat_idx_high = np.searchsorted(lats, lat)
            if lat_idx_high == 0:
                lat_idx_high = 1
            elif lat_idx_high >= len(lats):
                lat_idx_high = len(lats) - 1
            lat_idx_low = lat_idx_high - 1
        else:
            # 纬度递减排序，需要反向查找
            lat_idx_low = np.searchsorted(-lats, -lat)
            if lat_idx_low == 0:
                lat_idx_low = 1
            elif lat_idx_low >= len(lats):
                lat_idx_low = len(lats) - 1
            lat_idx_high = lat_idx_low - 1
        
        # 经度通常是递增的
        lon_idx_high = np.searchsorted(lons, lon)
        if lon_idx_high == 0:
            lon_idx_high = 1
        elif lon_idx_high >= len(lons):
            lon_idx_high = len(lons) - 1
        lon_idx_low = lon_idx_high - 1
        
        # 获取四个点的坐标
        lat_low = lats[lat_idx_low]
        lat_high = lats[lat_idx_high]
        lon_low = lons[lon_idx_low]
        lon_high = lons[lon_idx_high]
        
        # 获取四个点的值
        val_ll = data[lat_idx_low, lon_idx_low]
        val_lh = data[lat_idx_low, lon_idx_high]
        val_hl = data[lat_idx_high, lon_idx_low]
        val_hh = data[lat_idx_high, lon_idx_high]
        
        # 如果任何一个点是NaN，则返回NaN
        if np.isnan(val_ll) or np.isnan(val_lh) or np.isnan(val_hl) or np.isnan(val_hh):
            return float('nan')
        
        # 计算权重
        if lat_high == lat_low:
            w_lat_high = 0.5
            w_lat_low = 0.5
        else:
            w_lat_high = (lat - lat_low) / (lat_high - lat_low)
            w_lat_low = 1.0 - w_lat_high
            
        if lon_high == lon_low:
            w_lon_high = 0.5
            w_lon_low = 0.5
        else:
            w_lon_high = (lon - lon_low) / (lon_high - lon_low)
            w_lon_low = 1.0 - w_lon_high
        
        # 双线性插值
        val_l = w_lon_low * val_ll + w_lon_high * val_lh
        val_h = w_lon_low * val_hl + w_lon_high * val_hh
        val = w_lat_low * val_l + w_lat_high * val_h
        
        return float(val)
    
    def get_value(self, var_name: str, lat: float, lon: float) -> Optional[float]:
        """
        获取指定位置的变量值（使用双线性插值）
        
        Args:
            var_name: 变量名称 (u10, v10, swh, water_u, water_v)
            lat: 纬度
            lon: 经度
            
        Returns:
            Optional[float]: 插值后的变量值
        """
        if var_name not in self.variable_data:
            self.log(f"变量 {var_name} 的插值数据不存在", "error")
            return None
        
        var_info = self.variable_data[var_name]
        data = var_info['data']
        lats = var_info['lats']
        lons = var_info['lons']
        lat_range = var_info['lat_range']
        lon_range = var_info['lon_range']
        lat_ascending = var_info.get('lat_ascending', True)  # 默认为递增
        
        # 确保坐标在有效范围内
        lat = max(lat_range[0], min(lat_range[1], lat))
        lon = max(lon_range[0], min(lon_range[1], lon))
        
        try:
            # 执行插值
            value = self.bilinear_interpolate(data, lats, lons, lat, lon, lat_ascending)
            
            # 如果结果是NaN，直接返回
            if np.isnan(value):
                return value
            
            # 检查插值结果是否在有效范围内
            if var_name in self.variable_valid_ranges:
                valid_min, valid_max = self.variable_valid_ranges[var_name]
                if value < valid_min or value > valid_max:
                    self.log(f"警告: {var_name} 在位置 ({lat}, {lon}) 的插值结果 {value} 超出有效范围 [{valid_min}, {valid_max}]，将使用有效范围的边界值", "warning")
                    value = max(valid_min, min(valid_max, value))
            
            return value
        except Exception as e:
            self.log(f"插值 {var_name} 在位置 ({lat}, {lon}) 失败: {e}", "error")
            return None
    
    def get_values(self, var_names: List[str], lat: float, lon: float) -> Dict[str, Optional[float]]:
        """
        获取指定位置的多个变量值
        
        Args:
            var_names: 变量名称列表
            lat: 纬度
            lon: 经度
            
        Returns:
            Dict[str, Optional[float]]: 变量名到插值值的映射
        """
        result = {}
        for var_name in var_names:
            result[var_name] = self.get_value(var_name, lat, lon)
        return result
    
    def get_wind(self, lat: float, lon: float) -> Tuple[Optional[float], Optional[float]]:
        """
        获取指定位置的风速分量
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (u10, v10) 风速分量
        """
        u10 = self.get_value('u10', lat, lon)
        v10 = self.get_value('v10', lat, lon)
        return u10, v10
    
    def get_wind_speed_direction(self, lat: float, lon: float) -> Tuple[Optional[float], Optional[float]]:
        """
        获取指定位置的风速和风向
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (风速, 风向)
            风向为气象风向，单位为度，0度表示北风，90度表示东风
        """
        u10, v10 = self.get_wind(lat, lon)
        
        if u10 is None or v10 is None or np.isnan(u10) or np.isnan(v10):
            return None, None
        
        # 计算风速
        speed = np.sqrt(u10**2 + v10**2)
        
        # 计算风向（气象风向，0度表示北风，90度表示东风）
        # 注意：气象风向是风的来向，与数学方向相反
        direction = np.degrees(np.arctan2(-u10, -v10))  # 使用负值是因为气象风向是风的来向
        if direction < 0:
            direction += 360.0
        
        return speed, direction
    
    def get_wave(self, lat: float, lon: float) -> Optional[float]:
        """
        获取指定位置的有效波高
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Optional[float]: 有效波高
        """
        # 直接使用get_value方法获取swh值
        swh = self.get_value('swh', lat, lon)
        
        # 如果是NaN值，直接返回
        if swh is None or np.isnan(swh):
            return swh
        
        # 确保swh值非负
        if swh < 0:
            self.log(f"警告: swh在位置 ({lat}, {lon}) 的插值结果为负值 {swh}，将替换为0", "warning")
            swh = 0.0
            
        return swh
    
    def get_current(self, lat: float, lon: float) -> Tuple[Optional[float], Optional[float]]:
        """
        获取指定位置的洋流速度分量
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (water_u, water_v) 洋流速度分量
        """
        water_u = self.get_value('water_u', lat, lon)
        water_v = self.get_value('water_v', lat, lon)
        return water_u, water_v
    
    def get_current_speed_direction(self, lat: float, lon: float) -> Tuple[Optional[float], Optional[float]]:
        """
        获取指定位置的洋流速度和方向
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (流速, 流向)
            流向为水流的去向，单位为度，0度表示向北流，90度表示向东流
        """
        water_u, water_v = self.get_current(lat, lon)
        
        if water_u is None or water_v is None or np.isnan(water_u) or np.isnan(water_v):
            return None, None
        
        # 计算流速
        speed = np.sqrt(water_u**2 + water_v**2)
        
        # 计算流向（水流的去向，0度表示向北流，90度表示向东流）
        direction = np.degrees(np.arctan2(water_u, water_v))
        if direction < 0:
            direction += 360.0
        
        return speed, direction
    
    def get_all_variables(self, lat: float, lon: float) -> Dict[str, Optional[float]]:
        """
        获取指定位置的所有变量值
        
        Args:
            lat: 纬度
            lon: 经度
            
        Returns:
            Dict[str, Optional[float]]: 所有变量的值
        """
        all_vars = []
        for vars_list in self.supported_variables.values():
            all_vars.extend(vars_list)
        
        return self.get_values(all_vars, lat, lon)
    
    def get_variable_info(self) -> Dict[str, Dict]:
        """
        获取所有变量的信息
        
        Returns:
            Dict[str, Dict]: 变量信息
        """
        info = {}
        for var_name in self.variable_data:
            info[var_name] = {
                'shape': self.variable_shapes.get(var_name, None),
                'range': self.variable_ranges.get(var_name, None),
                'valid_range': self.variable_valid_ranges.get(var_name, None),
                'lat_range': self.variable_data[var_name]['lat_range'],
                'lon_range': self.variable_data[var_name]['lon_range']
            }
        return info

# 性能测试函数
def test_interpolator_performance(interpolator: OceanInterpolator, n_points: int = 1000) -> None:
    """
    测试插值器的性能
    
    Args:
        interpolator: 插值器实例
        n_points: 测试点数量
    """
    if _output_enabled:
        print(f"\n性能测试: 插值 {n_points} 个随机点...")
    
    # 获取所有变量的有效范围
    var_info = interpolator.get_variable_info()
    if not var_info:
        print("没有可用的变量信息")
        return
    
    # 使用第一个变量的范围作为测试范围
    first_var = list(var_info.keys())[0]
    lat_range = var_info[first_var]['lat_range']
    lon_range = var_info[first_var]['lon_range']
    
    # 生成随机测试点
    np.random.seed(42)  # 固定随机种子以便结果可重复
    test_lats = np.random.uniform(lat_range[0], lat_range[1], n_points)
    test_lons = np.random.uniform(lon_range[0], lon_range[1], n_points)
    
    # 测试单变量插值性能
    for var_name in var_info:
        start_time = time.time()
        for i in range(n_points):
            _ = interpolator.get_value(var_name, test_lats[i], test_lons[i])
        elapsed = time.time() - start_time
        if _output_enabled:
            print(f"{var_name}: {elapsed:.3f} 秒, {n_points/elapsed:.1f} 点/秒")
    
    # 测试多变量同时插值性能
    all_vars = list(var_info.keys())
    start_time = time.time()
    for i in range(n_points):
        _ = interpolator.get_values(all_vars, test_lats[i], test_lons[i])
    elapsed = time.time() - start_time
    if _output_enabled:
        print(f"所有变量: {elapsed:.3f} 秒, {n_points/elapsed:.1f} 点/秒")


# 直接运行时执行主函数
if __name__ == "__main__":
    enable_output()  # 仅在直接运行时启用输出
    
    print("海洋数据快速插值器")
    print("=" * 50)
    
    # 创建插值器
    data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "extracted_ocean_data.pkl")
    interpolator = OceanInterpolator(data_path, verbose=True)
    
    # 显示变量信息
    var_info = interpolator.get_variable_info()
    print("\n变量信息:")
    for var_name, info in var_info.items():
        print(f"{var_name}:")
        print(f"  原始形状: {info['shape']}")
        if info['range']:
            print(f"  数据范围: [{info['range']['min']:.4f}, {info['range']['max']:.4f}], 均值: {info['range']['mean']:.4f}")
        if info['valid_range']:
            print(f"  有效范围: [{info['valid_range'][0]:.4f}, {info['valid_range'][1]:.4f}]")
        print(f"  有效纬度范围: [{info['lat_range'][0]:.4f}, {info['lat_range'][1]:.4f}]")
        print(f"  有效经度范围: [{info['lon_range'][0]:.4f}, {info['lon_range'][1]:.4f}]")
    
    # 测试特定位置的插值
    test_lat, test_lon = 30.0, 125.0  # 测试位置
    print(f"\n测试位置 ({test_lat}, {test_lon}) 的插值结果:")
    
    # 获取风速
    u10, v10 = interpolator.get_wind(test_lat, test_lon)
    print(f"风速分量: u10 = {u10:.3f} m/s, v10 = {v10:.3f} m/s")
    
    # 获取风速和风向
    wind_speed, wind_dir = interpolator.get_wind_speed_direction(test_lat, test_lon)
    print(f"风速和风向: {wind_speed:.3f} m/s, {wind_dir:.1f}°")
    
    # 获取波高
    swh = interpolator.get_wave(test_lat, test_lon)
    print(f"有效波高: swh = {swh:.3f} m")
    
    # 获取洋流
    water_u, water_v = interpolator.get_current(test_lat, test_lon)
    print(f"洋流分量: water_u = {water_u:.3f} m/s, water_v = {water_v:.3f} m/s")
    
    # 获取洋流速度和方向
    current_speed, current_dir = interpolator.get_current_speed_direction(test_lat, test_lon)
    print(f"洋流速度和方向: {current_speed:.3f} m/s, {current_dir:.1f}°")
    
    # 性能测试
    test_interpolator_performance(interpolator)