#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试绘制功能的脚本
"""

import os
import sys
from improved_plot_training import plot_training_results_improved

def test_plotting():
    """测试绘制功能"""
    print("=" * 60)
    print("训练结果绘制测试")
    print("=" * 60)
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    logs_dir = os.path.join(project_root, "logs")
    
    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"日志目录: {logs_dir}")
    
    # 检查日志目录是否存在
    if not os.path.exists(logs_dir):
        print(f"错误: 日志目录不存在: {logs_dir}")
        return
    
    # 列出所有日志子目录
    log_subdirs = []
    for item in os.listdir(logs_dir):
        item_path = os.path.join(logs_dir, item)
        if os.path.isdir(item_path):
            log_subdirs.append(item)
    
    if not log_subdirs:
        print("未找到任何日志子目录")
        return
    
    print(f"\n找到 {len(log_subdirs)} 个日志目录:")
    for i, subdir in enumerate(log_subdirs, 1):
        subdir_path = os.path.join(logs_dir, subdir)
        monitor_files = [f for f in os.listdir(subdir_path) if f.endswith('.monitor.csv')]
        print(f"  {i}. {subdir} (包含 {len(monitor_files)} 个monitor文件)")
    
    # 让用户选择要测试的目录
    while True:
        try:
            choice = input(f"\n请选择要测试的日志目录 (1-{len(log_subdirs)}, 或按回车选择最新的): ")
            
            if choice.strip() == "":
                # 选择最新的目录
                selected_dir = max(log_subdirs)
                print(f"自动选择最新目录: {selected_dir}")
                break
            else:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(log_subdirs):
                    selected_dir = log_subdirs[choice_idx]
                    print(f"选择目录: {selected_dir}")
                    break
                else:
                    print(f"无效选择，请输入 1-{len(log_subdirs)} 之间的数字")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n测试已取消")
            return
    
    # 测试选定的目录
    selected_path = os.path.join(logs_dir, selected_dir)
    print(f"\n开始测试目录: {selected_path}")
    print("-" * 60)
    
    try:
        plot_training_results_improved(selected_path)
        print("\n测试完成！")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_all_directories():
    """测试所有日志目录"""
    print("=" * 60)
    print("测试所有日志目录")
    print("=" * 60)
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    logs_dir = os.path.join(project_root, "logs")
    
    if not os.path.exists(logs_dir):
        print(f"错误: 日志目录不存在: {logs_dir}")
        return
    
    # 获取所有日志子目录
    log_subdirs = []
    for item in os.listdir(logs_dir):
        item_path = os.path.join(logs_dir, item)
        if os.path.isdir(item_path):
            log_subdirs.append(item)
    
    if not log_subdirs:
        print("未找到任何日志子目录")
        return
    
    print(f"找到 {len(log_subdirs)} 个日志目录，开始逐个测试...\n")
    
    for i, subdir in enumerate(log_subdirs, 1):
        print(f"[{i}/{len(log_subdirs)}] 测试目录: {subdir}")
        print("-" * 40)
        
        subdir_path = os.path.join(logs_dir, subdir)
        try:
            plot_training_results_improved(subdir_path)
            print(f"✓ {subdir} 测试成功\n")
        except Exception as e:
            print(f"✗ {subdir} 测试失败: {e}\n")
    
    print("所有目录测试完成！")

if __name__ == "__main__":
    print("训练结果绘制测试工具")
    print("1. 测试单个目录")
    print("2. 测试所有目录")
    
    try:
        choice = input("请选择测试模式 (1/2): ")
        
        if choice == "1":
            test_plotting()
        elif choice == "2":
            test_all_directories()
        else:
            print("无效选择，默认使用单个目录测试模式")
            test_plotting()
    
    except KeyboardInterrupt:
        print("\n程序已退出")