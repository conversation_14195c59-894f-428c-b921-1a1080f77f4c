# 障碍物配置文件
# 格式: {num_obstacles: [(x, y, radius), ...], ...}

OBSTACLE_CONFIGS = {
    # 8个障碍物配置
    8: [
        (20, 20, 3),   # (x, y, radius)
        (80, 30, 4),
        (50, 70, 4),
        (30, 80, 4),
        (70, 15, 4),
        (15, 60, 3),
        (85, 85, 3),
        (60, 40, 4),
    ],
    
    # 13个障碍物配置（原始配置）
    13: [
        (20, 20, 3),   # (x, y, radius)
        (80, 30, 4),
        (50, 70, 4),
        (30, 80, 4),
        (70, 15, 4),
        (15, 60, 3),
        (85, 60, 3),
        (60, 40, 4),
        (40, 15, 3),
        (90, 90, 3),
        (15, 40, 3),
        (70, 85, 3),
        (60, 60, 3),
    ],
    
    # 5个障碍物配置（简单场景）
    5: [
        (20, 20, 3),
        (80, 30, 4),
        (50, 70, 4),
        (30, 80, 4),
        (70, 15, 4),
    ],
    
    # 10个障碍物配置
    10: [
        (20, 20, 3),
        (80, 30, 4),
        (50, 70, 4),
        (30, 80, 4),
        (70, 15, 4),
        (15, 60, 3),
        (85, 85, 3),
        (60, 40, 4),
        (10, 10, 3),
        (90, 90, 3),
    ],
    
    # 24个障碍物配置（复杂场景，均匀分布）
    24: [
        (12, 18, 2.5),   # 左下角区域
        (28, 12, 3.0),
        (42, 18, 2.5),
        (58, 12, 3.0),
        (72, 18, 2.5),   # 右下角区域
        (88, 12, 3.0),
        (18, 32, 2.5),   # 左侧下方
        (35, 35, 3.0),
        (52, 32, 2.5),   # 中央下方
        (68, 35, 3.0),
        (82, 32, 2.5),   # 右侧下方
        (12, 48, 3.0),   # 左侧中央
        (28, 52, 2.5),
        (45, 48, 3.0),   # 中央
        (62, 52, 2.5),
        (78, 48, 3.2),   # 右侧中央
        (88, 52, 2.5),
        (18, 68, 3.0),   # 左侧上方
        (35, 65, 2.5),
        (52, 68, 3.2),   # 中央上方
        (68, 65, 2.5),
        (82, 68, 3.0),   # 右侧上方
        (28, 82, 2.5),   # 左上角区域
        (72, 82, 3.0),   # 右上角区域
    ],
}

def get_obstacle_config(num_obstacles):
    """
    获取指定数量的障碍物配置
    如果指定数量不存在，则返回最接近的配置
    """
    if num_obstacles in OBSTACLE_CONFIGS:
        return OBSTACLE_CONFIGS[num_obstacles]
    
    # 找到最接近的配置
    available_nums = list(OBSTACLE_CONFIGS.keys())
    closest_num = min(available_nums, key=lambda x: abs(x - num_obstacles))
    print(f"警告：没有找到{num_obstacles}个障碍物的配置，使用{closest_num}个障碍物的配置代替")
    return OBSTACLE_CONFIGS[closest_num]