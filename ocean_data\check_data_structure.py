#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查海洋数据结构
"""

import pickle
import numpy as np
import os

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(current_dir, 'extracted_ocean_data.pkl')

print(f"加载数据文件: {data_path}")

try:
    with open(data_path, 'rb') as f:
        data = pickle.load(f)
    
    print("数据类型:", type(data))
    print("\n主键:", list(data.keys()))
    
    for key in data.keys():
        print(f"\n{key}数据:")
        print(f"  键: {list(data[key].keys())}")
        
        if 'coordinates' in data[key]:
            coords = data[key]['coordinates']
            print(f"  坐标键: {list(coords.keys())}")
            
            if 'latitude' in coords and 'longitude' in coords:
                lats = coords['latitude']
                lons = coords['longitude']
                print(f"  坐标范围: 经度 {lons.min():.2f}°-{lons.max():.2f}°, 纬度 {lats.min():.2f}°-{lats.max():.2f}°")
                print(f"  坐标形状: 纬度 {lats.shape}, 经度 {lons.shape}")
        
        if 'variables' in data[key]:
            vars_dict = data[key]['variables']
            print(f"  变量: {list(vars_dict.keys())}")
            
            for var_name, var_data in vars_dict.items():
                print(f"    {var_name}: 类型 {type(var_data).__name__}")
                
                if isinstance(var_data, dict) and 'data' in var_data:
                    print(f"      数据形状: {var_data['data'].shape}")
                    print(f"      数据类型: {var_data['data'].dtype}")
                    # 显示非NaN值的范围
                    if np.issubdtype(var_data['data'].dtype, np.number):
                        data_array = var_data['data']
                        valid_mask = ~np.isnan(data_array)
                        if np.any(valid_mask):
                            print(f"      数据范围: [{np.nanmin(data_array):.4f}, {np.nanmax(data_array):.4f}]")
                            print(f"      数据均值: {np.nanmean(data_array):.4f}")
                elif hasattr(var_data, 'shape'):
                    print(f"      数据形状: {var_data.shape}")
                    print(f"      数据类型: {var_data.dtype}")
                    # 显示非NaN值的范围
                    if np.issubdtype(var_data.dtype, np.number):
                        valid_mask = ~np.isnan(var_data)
                        if np.any(valid_mask):
                            print(f"      数据范围: [{np.nanmin(var_data):.4f}, {np.nanmax(var_data):.4f}]")
                            print(f"      数据均值: {np.nanmean(var_data):.4f}")

except Exception as e:
    print(f"错误: {e}")