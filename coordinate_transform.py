import numpy as np

def global_to_local(global_pos, robot_pos, robot_angle):
    """
    将全局坐标转换为以机器人为中心、前进方向为正方向的局部坐标
    
    参数:
        global_pos: 全局坐标系下的位置 [x, y] 或形状为 [..., 2] 的数组
        robot_pos: 机器人在全局坐标系下的位置 [x, y]
        robot_angle: 机器人在全局坐标系下的朝向角度（弧度）
        
    返回:
        local_pos: 局部坐标系下的位置 [x, y] 或形状为 [..., 2] 的数组
    """
    # 确保输入是numpy数组
    global_pos = np.array(global_pos)
    robot_pos = np.array(robot_pos)
    
    # 计算相对位置（平移）
    relative_pos = global_pos - robot_pos
    
    # 如果输入是单个点
    if relative_pos.shape == (2,):
        # 旋转矩阵（逆时针旋转-robot_angle）
        cos_angle = np.cos(-robot_angle)
        sin_angle = np.sin(-robot_angle)
        rotation_matrix = np.array([[cos_angle, -sin_angle], 
                                   [sin_angle, cos_angle]])
        
        # 应用旋转
        local_pos = np.dot(rotation_matrix, relative_pos)
    else:
        # 处理批量输入
        cos_angle = np.cos(-robot_angle)
        sin_angle = np.sin(-robot_angle)
        
        # 创建旋转矩阵
        rotation_matrix = np.array([[cos_angle, -sin_angle], 
                                   [sin_angle, cos_angle]])
        
        # 应用旋转到每个点
        # 如果relative_pos是2D数组，形状为[n, 2]
        if len(relative_pos.shape) == 2:
            local_pos = np.dot(relative_pos, rotation_matrix.T)
        else:
            # 处理更高维度的输入
            original_shape = relative_pos.shape
            reshaped = relative_pos.reshape(-1, 2)
            rotated = np.dot(reshaped, rotation_matrix.T)
            local_pos = rotated.reshape(original_shape)
    
    return local_pos

def local_to_global(local_pos, robot_pos, robot_angle):
    """
    将局部坐标转换为全局坐标
    
    参数:
        local_pos: 局部坐标系下的位置 [x, y] 或形状为 [..., 2] 的数组
        robot_pos: 机器人在全局坐标系下的位置 [x, y]
        robot_angle: 机器人在全局坐标系下的朝向角度（弧度）
        
    返回:
        global_pos: 全局坐标系下的位置 [x, y] 或形状为 [..., 2] 的数组
    """
    # 确保输入是numpy数组
    local_pos = np.array(local_pos)
    robot_pos = np.array(robot_pos)
    
    # 如果输入是单个点
    if local_pos.shape == (2,):
        # 旋转矩阵（顺时针旋转robot_angle）
        cos_angle = np.cos(robot_angle)
        sin_angle = np.sin(robot_angle)
        rotation_matrix = np.array([[cos_angle, -sin_angle], 
                                   [sin_angle, cos_angle]])
        
        # 应用旋转
        rotated_pos = np.dot(rotation_matrix, local_pos)
        
        # 平移回全局坐标
        global_pos = rotated_pos + robot_pos
    else:
        # 处理批量输入
        cos_angle = np.cos(robot_angle)
        sin_angle = np.sin(robot_angle)
        
        # 创建旋转矩阵
        rotation_matrix = np.array([[cos_angle, -sin_angle], 
                                   [sin_angle, cos_angle]])
        
        # 应用旋转到每个点
        # 如果local_pos是2D数组，形状为[n, 2]
        if len(local_pos.shape) == 2:
            rotated_pos = np.dot(local_pos, rotation_matrix.T)
            global_pos = rotated_pos + robot_pos
        else:
            # 处理更高维度的输入
            original_shape = local_pos.shape
            reshaped = local_pos.reshape(-1, 2)
            rotated = np.dot(reshaped, rotation_matrix.T)
            global_pos = rotated.reshape(original_shape) + robot_pos
    
    return global_pos

def normalize_angle(angle):
    """
    将角度归一化到[-π, π]范围内
    
    参数:
        angle: 角度（弧度）
        
    返回:
        normalized_angle: 归一化后的角度（弧度）
    """
    return np.arctan2(np.sin(angle), np.cos(angle))

def get_local_velocity(global_vel, robot_angle):
    """
    将全局速度转换为局部坐标系下的速度
    
    参数:
        global_vel: 全局坐标系下的速度 [vx, vy]
        robot_angle: 机器人在全局坐标系下的朝向角度（弧度）
        
    返回:
        local_vel: 局部坐标系下的速度 [vx, vy]
    """
    global_vel = np.array(global_vel)
    
    # 旋转矩阵（逆时针旋转-robot_angle）
    cos_angle = np.cos(-robot_angle)
    sin_angle = np.sin(-robot_angle)
    rotation_matrix = np.array([[cos_angle, -sin_angle], 
                               [sin_angle, cos_angle]])
    
    # 只进行旋转变换，不进行平移
    local_vel = np.dot(rotation_matrix, global_vel)
    return local_vel