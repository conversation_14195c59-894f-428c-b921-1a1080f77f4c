#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋数据与强化学习环境集成示例
Integration example of ocean data with RL environment
"""

import numpy as np
from ocean_data_loader import OceanDataLoader
import sys
import os

# 添加父目录到路径，以便导入环境模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class OceanEnvironmentIntegration:
    """
    海洋环境集成类
    用于将海洋数据集成到强化学习环境中
    """
    
    def __init__(self, ocean_data_dir="./ocean_data"):
        """
        初始化海洋环境集成
        
        Args:
            ocean_data_dir: 海洋数据目录
        """
        self.ocean_loader = OceanDataLoader(ocean_data_dir)
        self.data_loaded = False
        self.current_time_index = 0
        
    def load_ocean_data(self, wind_file=None, wave_file=None, current_file=None):
        """
        加载海洋数据
        
        Args:
            wind_file: 海风数据文件路径
            wave_file: 海浪数据文件路径
            current_file: 洋流数据文件路径
        """
        success_count = 0
        
        if wind_file and os.path.exists(wind_file):
            if self.ocean_loader.load_wind_data(wind_file):
                success_count += 1
        
        if wave_file and os.path.exists(wave_file):
            if self.ocean_loader.load_wave_data(wave_file):
                success_count += 1
        
        if current_file and os.path.exists(current_file):
            if self.ocean_loader.load_current_data(current_file):
                success_count += 1
        
        if success_count > 0:
            self.ocean_loader.setup_interpolators(self.current_time_index)
            self.data_loaded = True
            print(f"成功加载 {success_count} 个海洋数据文件")
        else:
            print("未能加载任何海洋数据文件")
        
        return self.data_loaded
    
    def get_ocean_features(self, robot_lat, robot_lon):
        """
        获取机器人位置的海洋要素特征
        
        Args:
            robot_lat: 机器人纬度
            robot_lon: 机器人经度
            
        Returns:
            np.ndarray: 海洋要素特征向量
        """
        if not self.data_loaded:
            return np.zeros(32)  # 返回零向量作为默认值
        
        try:
            # 提取4x4栅格数据
            grid_data = self.ocean_loader.extract_grid_data(robot_lat, robot_lon)
            
            # 将栅格数据转换为特征向量
            features = self._grid_to_features(grid_data)
            
            return features
            
        except Exception as e:
            print(f"海洋特征提取失败: {e}")
            return np.zeros(32)
    
    def _grid_to_features(self, grid_data):
        """
        将栅格数据转换为特征向量
        
        Args:
            grid_data: 栅格数据字典
            
        Returns:
            np.ndarray: 特征向量
        """
        features = []
        
        # 处理海风数据
        if 'wind' in grid_data:
            wind_features = self._process_wind_features(grid_data['wind'])
            features.extend(wind_features)
        else:
            features.extend([0.0] * 8)  # 8个海风特征
        
        # 处理海浪数据
        if 'wave' in grid_data:
            wave_features = self._process_wave_features(grid_data['wave'])
            features.extend(wave_features)
        else:
            features.extend([0.0] * 8)  # 8个海浪特征
        
        # 处理洋流数据
        if 'current' in grid_data:
            current_features = self._process_current_features(grid_data['current'])
            features.extend(current_features)
        else:
            features.extend([0.0] * 8)  # 8个洋流特征
        
        # 添加综合特征
        综合特征 = self._compute_综合特征(grid_data)
        features.extend(综合特征)
        
        return np.array(features, dtype=np.float32)
    
    def _process_wind_features(self, wind_data):
        """
        处理海风特征
        
        Args:
            wind_data: 海风栅格数据
            
        Returns:
            list: 海风特征列表
        """
        features = []
        
        for var_name, var_data in wind_data.items():
            # 统计特征：均值、标准差、最大值、最小值
            features.extend([
                np.mean(var_data),
                np.std(var_data),
                np.max(var_data),
                np.min(var_data)
            ])
        
        # 如果特征不足8个，用0填充
        while len(features) < 8:
            features.append(0.0)
        
        return features[:8]  # 确保返回8个特征
    
    def _process_wave_features(self, wave_data):
        """
        处理海浪特征
        
        Args:
            wave_data: 海浪栅格数据
            
        Returns:
            list: 海浪特征列表
        """
        features = []
        
        for var_name, var_data in wave_data.items():
            # 统计特征：均值、标准差、最大值、最小值
            features.extend([
                np.mean(var_data),
                np.std(var_data),
                np.max(var_data),
                np.min(var_data)
            ])
        
        # 如果特征不足8个，用0填充
        while len(features) < 8:
            features.append(0.0)
        
        return features[:8]  # 确保返回8个特征
    
    def _process_current_features(self, current_data):
        """
        处理洋流特征
        
        Args:
            current_data: 洋流栅格数据
            
        Returns:
            list: 洋流特征列表
        """
        features = []
        
        for var_name, var_data in current_data.items():
            # 统计特征：均值、标准差、最大值、最小值
            features.extend([
                np.mean(var_data),
                np.std(var_data),
                np.max(var_data),
                np.min(var_data)
            ])
        
        # 如果特征不足8个，用0填充
        while len(features) < 8:
            features.append(0.0)
        
        return features[:8]  # 确保返回8个特征
    
    def _compute_综合特征(self, grid_data):
        """
        计算综合海洋环境特征
        
        Args:
            grid_data: 所有栅格数据
            
        Returns:
            list: 综合特征列表
        """
        features = []
        
        # 计算海洋环境复杂度指标
        try:
            # 风浪相互作用强度
            if 'wind' in grid_data and 'wave' in grid_data:
                wind_intensity = 0
                wave_intensity = 0
                
                for var_data in grid_data['wind'].values():
                    wind_intensity += np.mean(np.abs(var_data))
                
                for var_data in grid_data['wave'].values():
                    wave_intensity += np.mean(var_data)
                
                interaction_strength = wind_intensity * wave_intensity
                features.append(interaction_strength)
            else:
                features.append(0.0)
            
            # 海洋环境变化梯度
            gradient_magnitude = 0
            for data_type in grid_data.values():
                for var_data in data_type.values():
                    # 计算梯度幅值
                    grad_x = np.gradient(var_data, axis=1)
                    grad_y = np.gradient(var_data, axis=0)
                    gradient_magnitude += np.mean(np.sqrt(grad_x**2 + grad_y**2))
            
            features.append(gradient_magnitude)
            
            # 环境稳定性指标（方差的平均值）
            stability_index = 0
            var_count = 0
            for data_type in grid_data.values():
                for var_data in data_type.values():
                    stability_index += np.var(var_data)
                    var_count += 1
            
            if var_count > 0:
                stability_index /= var_count
            
            features.append(stability_index)
            
            # 环境能量密度
            energy_density = 0
            for data_type in grid_data.values():
                for var_data in data_type.values():
                    energy_density += np.sum(var_data**2)
            
            features.append(energy_density)
            
        except Exception as e:
            print(f"综合特征计算失败: {e}")
            features = [0.0, 0.0, 0.0, 0.0]
        
        # 确保返回4个综合特征
        while len(features) < 4:
            features.append(0.0)
        
        return features[:4]
    
    def update_time_step(self, time_index):
        """
        更新时间步
        
        Args:
            time_index: 新的时间索引
        """
        if self.data_loaded and time_index != self.current_time_index:
            self.current_time_index = time_index
            self.ocean_loader.setup_interpolators(time_index)
            print(f"海洋数据时间步更新为: {time_index}")
    
    def get_feature_dimension(self):
        """
        获取特征维度
        
        Returns:
            int: 特征向量维度
        """
        return 32  # 8(风) + 8(浪) + 8(流) + 8(综合) = 32

def demo_integration():
    """
    演示海洋数据集成
    """
    print("=" * 50)
    print("海洋数据与强化学习环境集成演示")
    print("=" * 50)
    
    # 创建集成对象
    ocean_env = OceanEnvironmentIntegration()
    
    # 尝试加载数据
    data_dir = "./ocean_data"
    wind_file = os.path.join(data_dir, "wind.nc")
    wave_file = os.path.join(data_dir, "wave.nc")
    current_file = os.path.join(data_dir, "current.nc")
    
    success = ocean_env.load_ocean_data(wind_file, wave_file, current_file)
    
    if success:
        print("\n海洋数据加载成功，开始特征提取演示...")
        
        # 模拟机器人轨迹
        robot_trajectory = [
            (30.0, 120.0),
            (30.1, 120.1),
            (30.2, 120.2),
            (30.3, 120.3),
            (30.4, 120.4),
        ]
        
        print(f"\n特征维度: {ocean_env.get_feature_dimension()}")
        print("\n机器人轨迹海洋特征:")
        
        for i, (lat, lon) in enumerate(robot_trajectory):
            features = ocean_env.get_ocean_features(lat, lon)
            print(f"位置 {i+1} ({lat:.1f}, {lon:.1f}): 特征向量形状={features.shape}, 范围=[{features.min():.3f}, {features.max():.3f}]")
            
            # 显示前几个特征值
            print(f"  前8个特征值: {features[:8]}")
        
        print("\n演示完成！")
        
    else:
        print("\n未能加载海洋数据，请检查数据文件是否存在")
        print("可以运行 test_ocean_data.py 创建示例数据")
    
    print("=" * 50)

if __name__ == "__main__":
    demo_integration()