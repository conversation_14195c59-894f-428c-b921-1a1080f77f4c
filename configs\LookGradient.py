import numpy as np
import matplotlib.pyplot as plt

# 训练步数
steps = np.arange(0, 500, 1)

# 情况1：使用默认学习率 1e-3
lr_default = np.full_like(steps, 1e-3, dtype=float)
lr_default[100:] = 1e-4  # 100步后回调生效

# 情况2：使用极小初始学习率 1e-6
lr_small_init = np.full_like(steps, 1e-6, dtype=float)
lr_small_init[100:] = 1e-4  # 100步后回调生效

# 绘制对比
plt.figure(figsize=(12, 6))
plt.plot(steps, lr_default, 'r-', label='Default LR (1e-3)')
plt.plot(steps, lr_small_init, 'b-', label='Small Init LR (1e-6)')
plt.axvline(x=100, color='gray', linestyle='--', label='First Callback Execution')
plt.yscale('log')
plt.xlabel('Training Steps')
plt.ylabel('Learning Rate')
plt.title('Learning Rate Initialization Comparison')
plt.legend()
plt.grid(True)
plt.show()