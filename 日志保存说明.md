# TD3训练日志保存说明

## 问题描述
之前的代码中，模型参数文件保存到了`logs`目录中，但是训练日志（如TensorBoard日志、CSV日志等）没有正确保存。同时，训练环境使用的是DummyVecEnv而不是真正的并行环境。

## 解决方案
已对代码进行以下修改：

### 1. 添加日志记录器配置
在`train.py`中添加了以下导入：
```python
from stable_baselines3.common.logger import configure
```

### 2. 配置TensorBoard日志
在创建TD3模型时添加了`tensorboard_log`参数：
```python
model = create_custom_td3_model(
    # ... 其他参数
    tensorboard_log=log_dir  # 添加TensorBoard日志记录
)
```

### 3. 设置多种日志格式
配置了多种日志输出格式：
```python
# 配置日志记录器，确保训练日志保存到logs目录
new_logger = configure(log_dir, ["stdout", "csv", "tensorboard"])
model.set_logger(new_logger)
```

### 4. 添加TensorBoard日志名称
在训练时指定了TensorBoard日志名称：
```python
model.learn(
    # ... 其他参数
    tb_log_name="TD3_PathPlanning"  # TensorBoard日志名称
)
```

## 现在的文件保存结构

训练完成后，文件将分别保存在不同目录：

### 模型保存结构

```
models/
├── training_20250530_143022/           # 第一次训练（时间戳文件夹）
│   ├── best_model.zip                  # 最佳模型参数
│   ├── final_model.zip                 # 最终模型参数
│   ├── interrupted_model.zip           # 中断模型参数（如果有）
│   ├── td3_pathplanning_50000_steps.zip   # 检查点模型（每5万步保存）
│   ├── td3_pathplanning_100000_steps.zip
│   ├── curriculum_stage_1_simple.zip      # 课程学习阶段模型（如果使用课程学习）
│   └── curriculum_stage_2_medium.zip
├── training_20250530_183045/           # 第二次训练（时间戳文件夹）
│   ├── best_model.zip
│   ├── final_model.zip
│   └── ...
└── training_20250531_091230/           # 第三次训练（时间戳文件夹）
    ├── best_model.zip
    ├── final_model.zip
    └── ...
```

**文件夹组织说明：**
- 每次训练开始时，会在 `models/` 目录下创建一个以训练开始时间命名的子文件夹
- 时间戳格式：`training_YYYYMMDD_HHMMSS`（例如：training_20250530_143022）
- 该次训练的所有模型文件都保存在对应的时间戳文件夹中
- 不同训练会话的模型完全隔离，避免文件覆盖
- 便于模型版本管理、对比和历史追踪

### 日志文件保存在 `logs/` 目录：
```
logs/
└── td3_pathplanning_YYYYMMDD_HHMMSS/
    ├── evaluations/                # 评估结果
    ├── progress.csv                # CSV格式的训练进度日志
    ├── TD3_PathPlanning_1/         # TensorBoard日志目录
    │   └── events.out.tfevents.*   # TensorBoard事件文件
    └── training_curves.png         # 训练曲线图
```

## 如何查看日志

### 1. 查看TensorBoard日志
```bash
tensorboard --logdir=logs/td3_pathplanning_YYYYMMDD_HHMMSS/TD3_PathPlanning_1
```

### 2. 查看CSV日志
可以直接打开`progress.csv`文件查看训练进度数据。

### 3. 查看训练曲线
训练完成后会自动生成`training_curves.png`图片文件。

## 课程学习训练的文件结构

对于课程学习训练：

### 模型文件：
```
models/
├── curriculum_stage_1_简单.zip     # 第一阶段模型
├── curriculum_stage_2_中等.zip     # 第二阶段模型
└── curriculum_stage_3_困难.zip     # 第三阶段模型
```

### 日志文件：
```
logs/
└── td3_curriculum_YYYYMMDD_HHMMSS/
    ├── stage_1/                    # 第一阶段日志
    ├── stage_2/                    # 第二阶段日志
    ├── stage_3/                    # 第三阶段日志
    ├── TD3_Curriculum_Stage_1_1/   # 第一阶段TensorBoard日志
    ├── TD3_Curriculum_Stage_2_1/   # 第二阶段TensorBoard日志
    └── TD3_Curriculum_Stage_3_1/   # 第三阶段TensorBoard日志
```

## 并行训练优化

### 训练环境并行化
- **标准训练**：使用8个并行环境 (`SubprocVecEnv`) 进行真正的多进程并行训练
- **课程学习训练**：使用4个并行环境，适合渐进式训练
- **评估环境**：使用单进程 (`DummyVecEnv`)，确保评估结果的一致性

### 性能提升
- 多进程并行可以显著加速数据收集和训练过程
- 每个进程独立运行环境，避免GIL限制
- 提高CPU利用率，特别适合多核处理器

## 注意事项

1. **模型文件**统一保存在`models`目录下，便于模型管理和部署
2. **日志文件**统一保存在`logs`目录下，便于训练过程分析
3. 每次训练都会创建一个带时间戳的日志子目录
4. TensorBoard日志可以用于可视化训练过程
5. CSV日志可以用于后续的数据分析
6. 模型文件和日志文件分离，结构更清晰，便于版本管理
7. **并行训练**：使用SubprocVecEnv实现真正的多进程并行，显著提升训练速度