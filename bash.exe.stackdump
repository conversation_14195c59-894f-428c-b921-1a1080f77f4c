Stack trace:
Frame         Function      Args
0007FFFFA920  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9820) msys-2.0.dll+0x1FE8E
0007FFFFA920  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABF8) msys-2.0.dll+0x67F9
0007FFFFA920  000210046832 (000210286019, 0007FFFFA7D8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA920  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA920  000210068E24 (0007FFFFA930, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAC00  00021006A225 (0007FFFFA930, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB88030000 ntdll.dll
7FFB869D0000 KERNEL32.DLL
7FFB85560000 KERNELBASE.dll
7FFB87420000 USER32.dll
7FFB85940000 win32u.dll
7FFB86930000 GDI32.dll
7FFB859F0000 gdi32full.dll
000210040000 msys-2.0.dll
7FFB85120000 msvcp_win.dll
7FFB85280000 ucrtbase.dll
7FFB85EC0000 advapi32.dll
7FFB86090000 msvcrt.dll
7FFB87F40000 sechost.dll
7FFB85B20000 bcrypt.dll
7FFB85D30000 RPCRT4.dll
7FFB848A0000 CRYPTBASE.DLL
7FFB85970000 bcryptPrimitives.dll
7FFB86140000 IMM32.DLL
