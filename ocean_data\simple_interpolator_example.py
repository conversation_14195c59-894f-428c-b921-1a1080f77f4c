#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版海洋数据插值器使用示例
展示如何使用简化版插值器处理海洋数据
"""

import numpy as np
import pickle
import time
import os
import sys

# 导入简化版插值器函数
from simple_interpolator import bilinear_interpolate, get_value, get_wind_speed_direction, get_current_speed_direction

# 加载原始海洋数据进行测试
def load_ocean_data(data_path):
    """
    加载海洋数据用于测试
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        dict: 加载的数据
    """
    print(f"加载数据文件: {data_path}")
    try:
        with open(data_path, 'rb') as f:
            data = pickle.load(f)
        print("数据加载成功")
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

# 提取变量数据和坐标
def extract_variable_data(data, var_name):
    """
    从数据中提取变量数据和坐标
    
    Args:
        data: 加载的数据
        var_name: 变量名称
        
    Returns:
        tuple: (数据数组, 纬度数组, 经度数组, 纬度是否递增)
    """
    # 确定变量所属的数据类型
    data_type = None
    if var_name in ['u10', 'v10']:
        data_type = 'wind'
    elif var_name in ['swh']:
        data_type = 'wave'
    elif var_name in ['water_u', 'water_v']:
        data_type = 'current'
    
    if data_type is None or data_type not in data:
        print(f"未找到变量 {var_name} 的数据")
        return None, None, None, True
    
    # 获取坐标
    lats = data[data_type]['coordinates']['latitude']
    lons = data[data_type]['coordinates']['longitude']
    
    # 获取变量数据
    var_data = data[data_type]['variables'][var_name]
    if isinstance(var_data, dict) and 'data' in var_data:
        data_array = var_data['data']
    else:
        data_array = var_data
    
    # 处理多维数组，确保是2D数组
    if len(data_array.shape) > 2:
        if len(data_array.shape) == 4:  # (time, level, lat, lon)
            data_array = data_array[0, 0, :, :]
        elif len(data_array.shape) == 3:  # (time, lat, lon) 或 (level, lat, lon)
            data_array = data_array[0, :, :]
    
    # 确定纬度是否递增
    lat_ascending = np.all(np.diff(lats) > 0)
    
    return data_array, lats, lons, lat_ascending

# 主函数
def main():
    # 设置数据文件路径
    data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "extracted_ocean_data.pkl")
    
    # 加载数据
    data = load_ocean_data(data_path)
    if data is None:
        return
    
    # 提取变量数据
    print("\n提取变量数据...")
    variables = ['u10', 'v10', 'swh', 'water_u', 'water_v']
    var_data = {}
    
    for var_name in variables:
        data_array, lats, lons, lat_ascending = extract_variable_data(data, var_name)
        if data_array is not None:
            var_data[var_name] = {
                'data': data_array,
                'lats': lats,
                'lons': lons,
                'lat_ascending': lat_ascending
            }
            print(f"✓ 成功提取 {var_name} 数据，形状: {data_array.shape}")
    
    # 测试插值
    print("\n测试插值...")
    test_points = [
        (30.0, 125.0),  # 常规点
        (30.5, 125.5),  # 需要插值的点
        (29.8, 124.7)   # 另一个需要插值的点
    ]
    
    for test_lat, test_lon in test_points:
        print(f"\n位置 ({test_lat}, {test_lon}) 的插值结果:")
        
        # 获取风速
        u10 = get_value(var_data['u10']['data'], var_data['u10']['lats'], var_data['u10']['lons'], 
                      test_lat, test_lon, var_data['u10']['lat_ascending'])
        v10 = get_value(var_data['v10']['data'], var_data['v10']['lats'], var_data['v10']['lons'], 
                      test_lat, test_lon, var_data['v10']['lat_ascending'])
        print(f"风速分量: u10 = {u10:.3f} m/s, v10 = {v10:.3f} m/s")
        
        # 获取风速和风向
        wind_speed, wind_dir = get_wind_speed_direction(u10, v10)
        print(f"风速和风向: {wind_speed:.3f} m/s, {wind_dir:.1f}°")
        
        # 获取波高
        swh = get_value(var_data['swh']['data'], var_data['swh']['lats'], var_data['swh']['lons'], 
                      test_lat, test_lon, var_data['swh']['lat_ascending'])
        print(f"有效波高: swh = {swh:.3f} m")
        
        # 获取洋流
        water_u = get_value(var_data['water_u']['data'], var_data['water_u']['lats'], var_data['water_u']['lons'], 
                          test_lat, test_lon, var_data['water_u']['lat_ascending'])
        water_v = get_value(var_data['water_v']['data'], var_data['water_v']['lats'], var_data['water_v']['lons'], 
                          test_lat, test_lon, var_data['water_v']['lat_ascending'])
        print(f"洋流分量: water_u = {water_u:.3f} m/s, water_v = {water_v:.3f} m/s")
        
        # 获取洋流速度和方向
        current_speed, current_dir = get_current_speed_direction(water_u, water_v)
        print(f"洋流速度和方向: {current_speed:.3f} m/s, {current_dir:.1f}°")
    
    # 性能测试
    print("\n性能测试...")
    n_points = 10000
    
    # 获取第一个变量的范围作为测试范围
    first_var = list(var_data.keys())[0]
    lats = var_data[first_var]['lats']
    lons = var_data[first_var]['lons']
    lat_range = (lats.min(), lats.max())
    lon_range = (lons.min(), lons.max())
    
    # 生成随机测试点
    np.random.seed(42)  # 固定随机种子以便结果可重复
    test_lats = np.random.uniform(lat_range[0], lat_range[1], n_points)
    test_lons = np.random.uniform(lon_range[0], lon_range[1], n_points)
    
    # 测试单变量插值性能
    for var_name in var_data:
        start_time = time.time()
        for i in range(n_points):
            _ = get_value(var_data[var_name]['data'], var_data[var_name]['lats'], var_data[var_name]['lons'], 
                        test_lats[i], test_lons[i], var_data[var_name]['lat_ascending'])
        elapsed = time.time() - start_time
        print(f"{var_name}: {elapsed:.3f} 秒, {n_points/elapsed:.1f} 点/秒")

# 直接运行时执行主函数
if __name__ == "__main__":
    main()