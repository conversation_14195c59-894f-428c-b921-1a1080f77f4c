#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版路径规划系统启动脚本
自动检查依赖并启动系统
"""

import sys
import subprocess
import os

def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    print("检查依赖模块...")
    
    # 检查stable_baselines3
    try:
        import stable_baselines3
        print("✓ stable_baselines3 已安装")
    except ImportError:
        print("正在安装 stable-baselines3...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "stable-baselines3"])
            print("✓ stable-baselines3 安装成功")
        except subprocess.CalledProcessError:
            print("✗ 安装 stable-baselines3 失败")
            return False
    
    # 检查其他依赖
    required_modules = ['numpy', 'matplotlib', 'gymnasium']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 已安装")
        except ImportError:
            print(f"✗ 缺少模块: {module}")
            print(f"请安装: pip install {module}")
            return False
    
    return True

def check_project_files():
    """检查项目文件是否存在"""
    print("检查项目文件...")
    
    required_files = [
        'interactive_path_planning_up.py',
        'environment.py', 
        'coordinate_transform.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} 存在")
        else:
            print(f"✗ 缺少文件: {file}")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("优化版交互式路径规划系统启动器")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查项目文件
    if not check_project_files():
        print("\n✗ 项目文件检查失败")
        input("按回车键退出...")
        return False
    
    # 检查依赖
    if not check_and_install_dependencies():
        print("\n✗ 依赖检查失败")
        input("按回车键退出...")
        return False
    
    print("\n" + "=" * 50)
    print("启动优化版路径规划系统...")
    print("=" * 50)
    
    try:
        # 导入并运行优化版系统
        from interactive_path_planning_up import main as run_optimized
        run_optimized()
        return True
        
    except Exception as e:
        print(f"\n✗ 系统运行出错: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
