import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
from stable_baselines3 import TD3
from gym import spaces

class CustomFeatureExtractor(BaseFeaturesExtractor):
    """
    自定义特征提取器，分别处理雷达数据、状态信息和海洋数据
    """
    def __init__(self, observation_space: spaces.Box, features_dim: int = 256):
        super(CustomFeatureExtractor, self).__init__(observation_space, features_dim)
        
        # 输入维度分解
        # 36维雷达 + 6维状态(位置2+目标2+速度2) + 2维相对信息(距离+角度) = 44维基本信息
        # 海洋数据: 4x4网格 x 5个变量 = 80维
        self.radar_dim = 36
        self.state_dim = 6
        self.relative_dim = 2
        self.basic_dim = self.radar_dim + self.state_dim + self.relative_dim  # 44维
        
        # 检查是否包含海洋数据
        self.has_ocean_data = observation_space.shape[0] > self.basic_dim
        
        if self.has_ocean_data:
            self.ocean_grid_size = 4
            self.ocean_var_count = 5  # u10, v10, swh, water_u, water_v
            self.ocean_data_dim = self.ocean_grid_size * self.ocean_grid_size * self.ocean_var_count
            print(f"检测到海洋数据输入，维度: {self.ocean_data_dim}")
        else:
            self.ocean_data_dim = 0
            print("未检测到海洋数据输入")
        
        # 雷达数据处理网络
        self.radar_net = nn.Sequential(
            nn.Linear(self.radar_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 256),
            nn.ReLU(),
            nn.Linear(256, 64),
            nn.ReLU()
        )
        
        # 状态信息处理网络
        self.state_net = nn.Sequential(
            nn.Linear(self.state_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU()
        )
        
        # 相对信息处理网络
        self.relative_net = nn.Sequential(
            nn.Linear(self.relative_dim, 16),
            nn.ReLU(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU()
        )
        
        # 海洋数据处理网络 (使用卷积网络)
        if self.has_ocean_data:
            # 卷积网络处理4x4x5的海洋数据
            self.ocean_conv_net = nn.Sequential(
                # 将海洋数据重塑为 [batch_size, channels, height, width]
                # 输入: [batch_size, 5, 4, 4]
                nn.Conv2d(self.ocean_var_count, 16, kernel_size=2, stride=1, padding=0),
                # 输出: [batch_size, 16, 3, 3]
                nn.ReLU(),
                nn.Conv2d(16, 32, kernel_size=2, stride=1, padding=0),
                # 输出: [batch_size, 32, 2, 2]
                nn.ReLU(),
                nn.Flatten(),  # 输出: [batch_size, 32*2*2] = [batch_size, 128]
                nn.Linear(128, 64),
                nn.ReLU()
            )
        
        # 特征融合网络
        ocean_features_dim = 64 if self.has_ocean_data else 0
        total_features = 64 + 32 + 16 + ocean_features_dim  # 112或176维
        self.fusion_net = nn.Sequential(
            nn.Linear(total_features, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU(),
            nn.Linear(128, features_dim),
            nn.ReLU()
        )
        
    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        # 分解输入
        radar_data = observations[:, :self.radar_dim]
        state_data = observations[:, self.radar_dim:self.radar_dim + self.state_dim]
        relative_data = observations[:, self.radar_dim + self.state_dim:self.basic_dim]
        
        # 分别处理基本数据
        radar_features = self.radar_net(radar_data)
        state_features = self.state_net(state_data)
        relative_features = self.relative_net(relative_data)
        
        # 处理海洋数据（如果有）
        if self.has_ocean_data:
            ocean_data = observations[:, self.basic_dim:]
            # 重塑为 [batch_size, channels, height, width]
            batch_size = ocean_data.shape[0]
            ocean_data_reshaped = ocean_data.view(batch_size, self.ocean_var_count, 
                                               self.ocean_grid_size, self.ocean_grid_size)
            ocean_features = self.ocean_conv_net(ocean_data_reshaped)
            
            # 特征融合（包含海洋数据）
            combined_features = torch.cat([radar_features, state_features, relative_features, ocean_features], dim=1)
        else:
            # 特征融合（不包含海洋数据）
            combined_features = torch.cat([radar_features, state_features, relative_features], dim=1)
        
        fused_features = self.fusion_net(combined_features)
        
        return fused_features

class CustomActorNetwork(nn.Module):
    """
    自定义Actor网络
    """
    def __init__(self, features_dim: int = 256, action_dim: int = 2):
        super(CustomActorNetwork, self).__init__()
        
        self.actor_net = nn.Sequential(
            nn.Linear(features_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            # nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, action_dim),
            nn.Tanh()  # 输出范围[-1, 1]
        )
        
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        return self.actor_net(features)

class CustomCriticNetwork(nn.Module):
    """
    自定义Critic网络
    """
    def __init__(self, features_dim: int = 256, action_dim: int = 2):
        super(CustomCriticNetwork, self).__init__()
        
        # 第一个Q网络
        self.q1_net = nn.Sequential(
            nn.Linear(features_dim + action_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            # nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
        # 第二个Q网络 (TD3需要双Q网络)
        self.q2_net = nn.Sequential(
            nn.Linear(features_dim + action_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            # nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )
        
    def forward(self, features: torch.Tensor, actions: torch.Tensor) -> tuple:
        # 连接特征和动作
        combined = torch.cat([features, actions], dim=1)
        
        q1_value = self.q1_net(combined)
        q2_value = self.q2_net(combined)
        
        return q1_value, q2_value

# 自定义TD3策略没有用到，不用管
class CustomTD3Policy(nn.Module):
    """
    自定义TD3策略，整合特征提取器和Actor-Critic网络
    """
    def __init__(self, observation_space, action_space, features_dim: int = 256):
        super(CustomTD3Policy, self).__init__()
        
        self.features_extractor = CustomFeatureExtractor(observation_space, features_dim)
        self.actor = CustomActorNetwork(features_dim, action_space.shape[0])
        self.critic = CustomCriticNetwork(features_dim, action_space.shape[0])
        
    def extract_features(self, observations: torch.Tensor) -> torch.Tensor:
        return self.features_extractor(observations)
    
    def forward_actor(self, observations: torch.Tensor) -> torch.Tensor:
        features = self.extract_features(observations)
        return self.actor(features)
    
    def forward_critic(self, observations: torch.Tensor, actions: torch.Tensor) -> tuple:
        features = self.extract_features(observations)
        return self.critic(features, actions)

def create_custom_td3_model(env, learning_rate=3e-4, buffer_size=1000000, 
                           learning_starts=25000, batch_size=512, tau=0.005, 
                           gamma=0.99, train_freq=1, gradient_steps=1,
                           policy_delay=2, target_policy_noise=0.2, 
                           target_noise_clip=0.5, device='auto', tensorboard_log=None):
    """
    创建自定义TD3模型的便捷函数
    learning_starts是用于开始学习的总环境步数
    policy_delay是每更新多少次策略
    target_policy_noise是目标策略的噪声标准差
    target_noise_clip是目标策略的噪声上限
    """
    
    # 自定义策略配置
    policy_kwargs = {
        'features_extractor_class': CustomFeatureExtractor,
        'features_extractor_kwargs': {'features_dim': 256},
        'net_arch': {
            'pi': [256, 128],  # Actor网络结构
            'qf': [256, 128]   # Critic网络结构
        },
        'activation_fn': torch.nn.ReLU,
    }
    
    model = TD3(
        policy="MlpPolicy",
        env=env,
        learning_rate=learning_rate,
        buffer_size=buffer_size,
        learning_starts=learning_starts,
        batch_size=batch_size,
        tau=tau,
        gamma=gamma,
        train_freq=train_freq,
        gradient_steps=gradient_steps,
        policy_delay=policy_delay,
        target_policy_noise=target_policy_noise,
        target_noise_clip=target_noise_clip,
        policy_kwargs=policy_kwargs,
        verbose=1,
        device=device,
        seed=42, # 固定随机种子
        tensorboard_log=tensorboard_log  # 添加TensorBoard日志支持
    )
    
    return model

class RadarProcessor:
    """
    雷达数据处理工具类
    """
    @staticmethod
    def normalize_radar_data(radar_readings, max_range=15.0):
        """归一化雷达数据"""
        return np.clip(radar_readings / max_range, 0.0, 1.0)
    
    @staticmethod
    def smooth_radar_data(radar_readings, window_size=3):
        """平滑雷达数据以减少噪声"""
        if window_size <= 1:
            return radar_readings
        
        smoothed = np.convolve(radar_readings, 
                              np.ones(window_size)/window_size, 
                              mode='same')
        return smoothed
    
    @staticmethod
    def extract_radar_features(radar_readings):
        """从雷达数据中提取高级特征"""
        features = {}
        
        # 基本统计特征
        features['min_distance'] = np.min(radar_readings)
        features['max_distance'] = np.max(radar_readings)
        features['mean_distance'] = np.mean(radar_readings)
        features['std_distance'] = np.std(radar_readings)
        
        # 梯度特征
        gradients = np.gradient(radar_readings)
        features['max_gradient'] = np.max(np.abs(gradients))
        features['mean_gradient'] = np.mean(np.abs(gradients))
        
        # 障碍物密度
        close_threshold = 0.3  # 归一化后的阈值
        features['obstacle_density'] = np.sum(radar_readings < close_threshold) / len(radar_readings)
        
        return features