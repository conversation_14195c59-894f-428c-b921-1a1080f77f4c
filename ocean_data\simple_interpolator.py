#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版海洋数据插值器
只需要传入经纬度和数据就能进行插值，不需要加载数据文件
"""

import numpy as np
from typing import Optional, Tuple, Dict, List


def bilinear_interpolate(data: np.ndarray, lats: np.ndarray, lons: np.ndarray, 
                        lat: float, lon: float, lat_ascending: bool = True) -> float:
    """
    使用双线性插值计算指定位置的值
    如果四个插值点中有任何一个是NaN，则返回NaN
    
    Args:
        data: 2D数据数组
        lats: 纬度坐标数组
        lons: 经度坐标数组
        lat: 目标纬度
        lon: 目标经度
        lat_ascending: 纬度是否递增排序
        
    Returns:
        float: 插值结果
    """
    # 确保坐标在有效范围内
    lat = max(min(lat, lats.max()), lats.min())
    lon = max(min(lon, lons.max()), lons.min())
    
    # 找到最近的网格点索引
    # 根据纬度排序方向选择合适的方法找到索引
    if lat_ascending:
        # 纬度递增排序
        lat_idx_high = np.searchsorted(lats, lat)
        if lat_idx_high == 0:
            lat_idx_high = 1
        elif lat_idx_high >= len(lats):
            lat_idx_high = len(lats) - 1
        lat_idx_low = lat_idx_high - 1
    else:
        # 纬度递减排序，需要反向查找
        lat_idx_low = np.searchsorted(-lats, -lat)
        if lat_idx_low == 0:
            lat_idx_low = 1
        elif lat_idx_low >= len(lats):
            lat_idx_low = len(lats) - 1
        lat_idx_high = lat_idx_low - 1
    
    # 经度通常是递增的
    lon_idx_high = np.searchsorted(lons, lon)
    if lon_idx_high == 0:
        lon_idx_high = 1
    elif lon_idx_high >= len(lons):
        lon_idx_high = len(lons) - 1
    lon_idx_low = lon_idx_high - 1
    
    # 获取四个点的坐标
    lat_low = lats[lat_idx_low]
    lat_high = lats[lat_idx_high]
    lon_low = lons[lon_idx_low]
    lon_high = lons[lon_idx_high]
    
    # 获取四个点的值
    val_ll = data[lat_idx_low, lon_idx_low]
    val_lh = data[lat_idx_low, lon_idx_high]
    val_hl = data[lat_idx_high, lon_idx_low]
    val_hh = data[lat_idx_high, lon_idx_high]
    
    # 如果任何一个点是NaN，则返回NaN
    if np.isnan(val_ll) or np.isnan(val_lh) or np.isnan(val_hl) or np.isnan(val_hh):
        return float('nan')
    
    # 计算权重
    if lat_high == lat_low:
        w_lat_high = 0.5
        w_lat_low = 0.5
    else:
        w_lat_high = (lat - lat_low) / (lat_high - lat_low)
        w_lat_low = 1.0 - w_lat_high
        
    if lon_high == lon_low:
        w_lon_high = 0.5
        w_lon_low = 0.5
    else:
        w_lon_high = (lon - lon_low) / (lon_high - lon_low)
        w_lon_low = 1.0 - w_lon_high
    
    # 双线性插值
    val_l = w_lon_low * val_ll + w_lon_high * val_lh
    val_h = w_lon_low * val_hl + w_lon_high * val_hh
    val = w_lat_low * val_l + w_lat_high * val_h
    
    return float(val)


def get_value(data: np.ndarray, lats: np.ndarray, lons: np.ndarray, 
             lat: float, lon: float, lat_ascending: bool = True, 
             valid_range: Optional[Tuple[float, float]] = None) -> Optional[float]:
    """
    获取指定位置的变量值（使用双线性插值）
    
    Args:
        data: 2D数据数组
        lats: 纬度坐标数组
        lons: 经度坐标数组
        lat: 目标纬度
        lon: 目标经度
        lat_ascending: 纬度是否递增排序
        valid_range: 有效值范围，如果提供，将检查插值结果是否在范围内
        
    Returns:
        Optional[float]: 插值后的变量值
    """
    # 确保坐标在有效范围内
    lat = max(lats.min(), min(lats.max(), lat))
    lon = max(lons.min(), min(lons.max(), lon))
    
    try:
        # 执行插值
        value = bilinear_interpolate(data, lats, lons, lat, lon, lat_ascending)
        
        # 如果结果是NaN，直接返回
        if np.isnan(value):
            return value
        
        # 检查插值结果是否在有效范围内
        if valid_range is not None:
            valid_min, valid_max = valid_range
            if value < valid_min or value > valid_max:
                value = max(valid_min, min(valid_max, value))
        
        return value
    except Exception as e:
        print(f"插值在位置 ({lat}, {lon}) 失败: {e}")
        return None


def get_wind_speed_direction(u10: float, v10: float) -> Tuple[Optional[float], Optional[float]]:
    """
    根据风速分量计算风速和风向
    
    Args:
        u10: 风速u分量
        v10: 风速v分量
        
    Returns:
        Tuple[Optional[float], Optional[float]]: (风速, 风向)
        风向为气象风向，单位为度，0度表示北风，90度表示东风
    """
    if u10 is None or v10 is None or np.isnan(u10) or np.isnan(v10):
        return None, None
    
    # 计算风速
    speed = np.sqrt(u10**2 + v10**2)
    
    # 计算风向（气象风向，0度表示北风，90度表示东风）
    # 注意：气象风向是风的来向，与数学方向相反
    direction = np.degrees(np.arctan2(-u10, -v10))  # 使用负值是因为气象风向是风的来向
    if direction < 0:
        direction += 360.0
    
    return speed, direction


def get_current_speed_direction(water_u: float, water_v: float) -> Tuple[Optional[float], Optional[float]]:
    """
    根据洋流分量计算洋流速度和方向
    
    Args:
        water_u: 洋流u分量
        water_v: 洋流v分量
        
    Returns:
        Tuple[Optional[float], Optional[float]]: (流速, 流向)
        流向为水流的去向，单位为度，0度表示向北流，90度表示向东流
    """
    if water_u is None or water_v is None or np.isnan(water_u) or np.isnan(water_v):
        return None, None
    
    # 计算流速
    speed = np.sqrt(water_u**2 + water_v**2)
    
    # 计算流向（水流的去向，0度表示向北流，90度表示向东流）
    direction = np.degrees(np.arctan2(water_u, water_v))
    if direction < 0:
        direction += 360.0
    
    return speed, direction


# 示例用法
if __name__ == "__main__":
    # 创建示例数据
    lats = np.array([30.0, 31.0, 32.0])
    lons = np.array([120.0, 121.0, 122.0])
    data = np.array([
        [1.0, 2.0, 3.0],
        [4.0, 5.0, 6.0],
        [7.0, 8.0, 9.0]
    ])
    
    # 测试插值
    test_lat, test_lon = 30.5, 121.5
    value = get_value(data, lats, lons, test_lat, test_lon)
    print(f"位置 ({test_lat}, {test_lon}) 的插值结果: {value}")
    
    # 测试风速和风向计算
    u10 = 5.0
    v10 = -3.0
    wind_speed, wind_dir = get_wind_speed_direction(u10, v10)
    print(f"风速: {wind_speed} m/s, 风向: {wind_dir}°")
    
    # 测试洋流速度和方向计算
    water_u = 0.2
    water_v = 0.3
    current_speed, current_dir = get_current_speed_direction(water_u, water_v)
    print(f"洋流速度: {current_speed} m/s, 洋流方向: {current_dir}°")