#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋数据提取工具
用于从extracted_ocean_data.pkl文件中提取特定变量数据
支持提取u10、v10、swh、water_u和water_v变量
"""

import numpy as np
import pickle
import os
from typing import Dict, Tuple, Optional, List, Union
import matplotlib.pyplot as plt

# 方法1：全局设置（永久生效） 中文不显示问题
plt.rcParams['font.family'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class OceanDataExtractor:
    """
    海洋数据提取器
    用于从预处理的pickle文件中提取特定变量数据
    """
    
    def __init__(self, data_path: str = "extracted_ocean_data.pkl"):
        """
        初始化数据提取器
        
        Args:
            data_path: pickle数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.variables = {
            'wind': ['u10', 'v10'],
            'wave': ['swh'],
            'current': ['water_u', 'water_v']
        }
        self.coordinates = {}
        self.extracted_vars = {}
        
    def load_data(self) -> bool:
        """
        加载pickle格式的海洋数据
        
        Returns:
            bool: 是否加载成功
        """
        if not os.path.exists(self.data_path):
            print(f"数据文件不存在: {self.data_path}")
            return False
        
        try:
            with open(self.data_path, 'rb') as f:
                self.data = pickle.load(f)
            
            print(f"从 {self.data_path} 加载数据成功")
            print(f"数据类型: {list(self.data.keys())}")
            
            # 提取坐标信息
            for data_type in self.data:
                if 'coordinates' in self.data[data_type]:
                    self.coordinates[data_type] = {
                        'latitude': self.data[data_type]['coordinates']['latitude'],
                        'longitude': self.data[data_type]['coordinates']['longitude']
                    }
            
            return True
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def extract_variables(self) -> Dict:
        """
        提取指定的变量数据
        
        Returns:
            Dict: 提取的变量数据
        """
        if self.data is None:
            print("请先加载数据")
            return {}
        
        extracted = {}
        
        for data_type, var_names in self.variables.items():
            if data_type not in self.data:
                print(f"数据中不包含 {data_type} 类型")
                continue
                
            extracted[data_type] = {}
            
            # 提取坐标
            if 'coordinates' in self.data[data_type]:
                extracted[data_type]['coordinates'] = self.data[data_type]['coordinates']
            
            # 提取变量
            extracted[data_type]['variables'] = {}
            for var_name in var_names:
                if 'variables' in self.data[data_type] and var_name in self.data[data_type]['variables']:
                    extracted[data_type]['variables'][var_name] = self.data[data_type]['variables'][var_name]
                    print(f"✓ 成功提取 {data_type}/{var_name} 变量数据")
                else:
                    print(f"✗ 未找到 {data_type}/{var_name} 变量数据")
        
        self.extracted_vars = extracted
        return extracted
    
    """
    Optional[Union[np.ndarray, Dict]]:
    Optional[T]
    来自模块 typing。表示“可以是 T 类型，也可以是 None”
    这是函数的返回值类型注解，意思是：这个函数可能返回以下三种情况之一：
    一个 np.ndarray 类型的对象（比如 NumPy 数组）
    一个 Dict 类型的对象（即字典）或者返回 None
    """
    def get_variable_data(self, var_name: str) -> Optional[Union[np.ndarray, Dict]]:
        """
        获取指定变量的数据
        
        Args:
            var_name: 变量名称 (u10, v10, swh, water_u, water_v)
            
        Returns:
            Optional[Union[np.ndarray, Dict]]: 变量数据数组或字典，如果不存在则返回None
        """
        if not self.extracted_vars:
            print("请先提取变量数据")
            return None
        
        # 确定变量所属的数据类型
        data_type = None
        for dt, vars in self.variables.items():
            if var_name in vars:
                data_type = dt
                break
        
        if data_type is None:
            print(f"不支持的变量名: {var_name}")
            return None
        
        if data_type in self.extracted_vars and 'variables' in self.extracted_vars[data_type]:
            if var_name in self.extracted_vars[data_type]['variables']:
                return self.extracted_vars[data_type]['variables'][var_name]
        
        print(f"变量 {var_name} 不存在于提取的数据中")
        return None
    
    def get_value_at_position(self, var_name: str, lat: float, lon: float) -> Optional[float]:
        """
        获取指定位置的变量值（使用最近邻插值）
        
        Args:
            var_name: 变量名称
            lat: 纬度
            lon: 经度
            
        Returns:
            Optional[float]: 插值后的变量值
        """
        # 确定变量所属的数据类型
        data_type = None
        for dt, vars in self.variables.items():
            if var_name in vars:
                data_type = dt
                break
        
        if data_type is None:
            print(f"不支持的变量名: {var_name}")
            return None
        
        # 获取数据和坐标
        var_data = self.get_variable_data(var_name)
        lats, lons = self.get_coordinates(data_type)
        
        if var_data is None or lats is None or lons is None:
            return None
            
        # 处理字典类型的变量数据
        if isinstance(var_data, dict):
            if 'data' in var_data and hasattr(var_data['data'], 'shape'):
                data = var_data['data']
            else:
                print(f"变量 {var_name} 的数据结构不支持插值")
                return None
        else:
            data = var_data
            
        # 处理多维数组，确保是2D数组
        if len(data.shape) > 2:
            # 对于4D数组 (time, level, lat, lon)，取第一个时间和第一个层级
            if len(data.shape) == 4:
                data = data[0, 0, :, :]
            # 对于3D数组 (time, lat, lon) 或 (level, lat, lon)，取第一个时间或层级
            elif len(data.shape) == 3:
                data = data[0, :, :]
        
        # 确保数据维度与坐标维度匹配
        if data.shape != (len(lats), len(lons)):
            # 如果维度不匹配但接近，可能是因为网格定义方式不同，尝试调整
            if abs(data.shape[0] - len(lats)) <= 1 and abs(data.shape[1] - len(lons)) <= 1:
                # 创建新的坐标网格，与数据维度匹配
                lat_min, lat_max = lats.min(), lats.max()
                lon_min, lon_max = lons.min(), lons.max()
                new_lats = np.linspace(lat_min, lat_max, data.shape[0])
                new_lons = np.linspace(lon_min, lon_max, data.shape[1])
                lats, lons = new_lats, new_lons
            else:
                print(f"警告: 数据维度 {data.shape} 与坐标维度 ({len(lats)}, {len(lons)}) 不匹配，无法插值")
                return None
        
        # 找到最近的网格点
        lat_idx = np.abs(lats - lat).argmin()
        lon_idx = np.abs(lons - lon).argmin()
        
        # 返回该点的值
        return float(data[lat_idx, lon_idx])
    
    def get_coordinates(self, data_type: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取指定数据类型的经纬度坐标
        
        Args:
            data_type: 数据类型 (wind, wave, current)
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (纬度数组, 经度数组)
        """
        if data_type not in self.coordinates:
            print(f"未找到 {data_type} 的坐标数据")
            return None, None
        
        return self.coordinates[data_type]['latitude'], self.coordinates[data_type]['longitude']
    
    def visualize_variable(self, var_name: str, save_path: Optional[str] = None):
        """
        可视化变量数据
        
        Args:
            var_name: 变量名称
            save_path: 保存路径，如果为None则显示图像
        """
        # 确定变量所属的数据类型
        data_type = None
        for dt, vars in self.variables.items():
            if var_name in vars:
                data_type = dt
                break
        
        if data_type is None:
            print(f"不支持的变量名: {var_name}")
            return
        
        # 获取数据和坐标
        var_data = self.get_variable_data(var_name)
        if var_data is None:
            print(f"无法获取变量 {var_name} 的数据")
            return
            
        # 处理字典类型的变量数据
        if isinstance(var_data, dict):
            if 'data' in var_data and hasattr(var_data['data'], 'shape'):
                data = var_data['data']
            else:
                print(f"变量 {var_name} 的数据结构不支持可视化")
                return
        else:
            data = var_data
            
        # 处理多维数组，确保是2D数组
        if len(data.shape) > 2:
            print(f"变量 {var_name} 是{len(data.shape)}维数组，尝试提取2D切片")
            # 对于4D数组 (time, level, lat, lon)，取第一个时间和第一个层级
            if len(data.shape) == 4:
                data = data[0, 0, :, :]
            # 对于3D数组 (time, lat, lon) 或 (level, lat, lon)，取第一个时间或层级
            elif len(data.shape) == 3:
                data = data[0, :, :]
            print(f"提取的2D切片形状: {data.shape}")
            
        # 获取坐标
        lats, lons = self.get_coordinates(data_type)
        
        if lats is None or lons is None:
            print(f"无法获取 {data_type} 的坐标数据")
            return
        
        # 确保数据维度与坐标维度匹配
        if data.shape != (len(lats), len(lons)):
            print(f"警告: 数据维度 {data.shape} 与坐标维度 ({len(lats)}, {len(lons)}) 不匹配")
            # 如果维度不匹配但接近，可能是因为网格定义方式不同，尝试调整
            if abs(data.shape[0] - len(lats)) <= 1 and abs(data.shape[1] - len(lons)) <= 1:
                print("尝试调整坐标网格以匹配数据维度")
                # 创建新的坐标网格，与数据维度匹配
                lat_min, lat_max = lats.min(), lats.max()
                lon_min, lon_max = lons.min(), lons.max()
                new_lats = np.linspace(lat_min, lat_max, data.shape[0])
                new_lons = np.linspace(lon_min, lon_max, data.shape[1])
                lats, lons = new_lats, new_lons
        
        # 创建网格
        # lon_grid 中的每一行都是 lons 的完整拷贝。
        # lat_grid 中的每一列都是 lats 的完整拷贝。
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制图像
        plt.figure(figsize=(10, 8))
        
        # 根据变量类型选择不同的可视化方法
        if var_name in ['u10', 'v10', 'water_u', 'water_v']:
            # 矢量场可视化
            if var_name in ['u10', 'water_u']:
                # 尝试找到对应的v分量
                v_name = 'v10' if var_name == 'u10' else 'water_v'
                v_data = self.get_variable_data(v_name)
                
                # 处理v分量数据
                if v_data is not None:
                    if isinstance(v_data, dict):
                        if 'data' in v_data and hasattr(v_data['data'], 'shape'):
                            v_data = v_data['data']
                        else:
                            v_data = None
                    
                    # 处理多维数组
                    if v_data is not None and len(v_data.shape) > 2:
                        if len(v_data.shape) == 4:
                            v_data = v_data[0, 0, :, :]
                        elif len(v_data.shape) == 3:
                            v_data = v_data[0, :, :]
                
                if v_data is not None and v_data.shape == data.shape:
                    # 降采样以便于可视化
                    step = max(1, len(lats) // 20)
                    # quiver是绘制矢量场的函数
                    plt.quiver(lon_grid[::step, ::step], lat_grid[::step, ::step], 
                              data[::step, ::step], v_data[::step, ::step])
                    plt.title(f"{var_name}/{v_name} 矢量场")
                else:
                    plt.pcolormesh(lon_grid, lat_grid, data, shading='auto')
                    plt.colorbar(label=var_name)
                    plt.title(f"{var_name} 分布")
            else:
                plt.pcolormesh(lon_grid, lat_grid, data, shading='auto')
                plt.colorbar(label=var_name)
                plt.title(f"{var_name} 分布")
        else:
            # 标量场可视化
            plt.pcolormesh(lon_grid, lat_grid, data, shading='auto')
            plt.colorbar(label=var_name)
            plt.title(f"{var_name} 分布")
        
        plt.xlabel('经度')
        plt.ylabel('纬度')
        plt.grid(True)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图像已保存到: {save_path}")
        else:
            plt.show()

def load_ocean_data(data_path: str = "extracted_ocean_data.pkl") -> Dict:
    """
    加载海洋数据的便捷函数
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        Dict: 包含提取变量的字典
    """
    extractor = OceanDataExtractor(data_path)
    if extractor.load_data():
        return extractor.extract_variables()
    return {}

def get_ocean_variable(data: Dict, var_name: str) -> Optional[np.ndarray]:
    """
    从加载的数据中获取指定变量
    
    Args:
        data: 加载的海洋数据
        var_name: 变量名称
        
    Returns:
        Optional[np.ndarray]: 变量数据
    """
    # 确定变量所属的数据类型
    data_type_map = {
        'u10': 'wind',
        'v10': 'wind',
        'swh': 'wave',
        'water_u': 'current',
        'water_v': 'current'
    }
    
    if var_name not in data_type_map:
        print(f"不支持的变量名: {var_name}")
        return None
    
    data_type = data_type_map[var_name]
    
    if data_type in data and 'variables' in data[data_type]:
        if var_name in data[data_type]['variables']:
            return data[data_type]['variables'][var_name]
    
    print(f"变量 {var_name} 不存在于数据中")
    return None

def main():
    """
    主函数 - 演示数据提取和使用
    整体数据格式如下：大字典包含小字典。
    {
        'wind': {
            'coordinates': {'latitude': array(...), 'longitude': array(...)},
            'variables': {'u10': array(...), 'v10': array(...)}
        },
        'wave': {
            'coordinates': {'latitude': array(...), 'longitude': array(...)},
            'variables': {'swh': array(...)}
        },
        'current': {
            'coordinates': {'latitude': array(...), 'longitude': array(...)},
            'variables': {'water_u': array(...), 'water_v': array(...)}
        }
    }                                       

    """
    print("海洋数据提取工具")
    print("=" * 50)
    
    # 创建提取器
    extractor = OceanDataExtractor()
    
    # 加载数据
    if not extractor.load_data():
        print("数据加载失败，退出程序")
        return
    
    # 提取变量
    extracted_data = extractor.extract_variables()
    
    # 打印提取的变量信息
    print("\n提取的变量信息:")
    for data_type, data_info in extracted_data.items():
        print(f"\n{data_type.upper()} 数据:")
        if 'coordinates' in data_info:
            lats = data_info['coordinates']['latitude']
            lons = data_info['coordinates']['longitude']
            print(f"  坐标范围: 经度 {lons.min():.2f}°-{lons.max():.2f}°, 纬度 {lats.min():.2f}°-{lats.max():.2f}°")
            print(f"  数据分辨率: {len(lats)} x {len(lons)}")
        
        if 'variables' in data_info:
            for var_name, var_data in data_info['variables'].items():
                # 检查数据类型，确保正确处理
                try:
                    if isinstance(var_data, dict):
                        # 如果是字典类型，显示字典结构
                        print(f"    变量 {var_name}: 字典类型，键: {list(var_data.keys())}")
                        # 如果字典包含数据数组，可以尝试显示其统计信息
                        if 'data' in var_data and hasattr(var_data['data'], 'shape'):
                            data_array = var_data['data']
                            if np.issubdtype(data_array.dtype, np.number):
                                valid_points = np.sum(~np.isnan(data_array))
                                total_points = data_array.size
                                valid_rate = valid_points/total_points*100 if total_points > 0 else 0
                                print(f"      数据形状: {data_array.shape}, 有效率: {valid_rate:.1f}%")
                                print(f"      范围: [{np.nanmin(data_array):.4f}, {np.nanmax(data_array):.4f}], 均值: {np.nanmean(data_array):.4f}")
                    elif hasattr(var_data, 'shape'):
                        # 如果是数组类型
                        if hasattr(var_data, 'dtype') and np.issubdtype(var_data.dtype, np.number):
                            valid_points = np.sum(~np.isnan(var_data))
                            total_points = var_data.size
                            valid_rate = valid_points/total_points*100 if total_points > 0 else 0
                            print(f"    变量 {var_name}: 形状 {var_data.shape}, 有效率 {valid_rate:.1f}%")
                            print(f"      范围: [{np.nanmin(var_data):.4f}, {np.nanmax(var_data):.4f}], 均值: {np.nanmean(var_data):.4f}")
                        else:
                            # 对于非数值类型数组，只显示形状信息
                            print(f"    变量 {var_name}: 形状 {var_data.shape}, 类型 {type(var_data).__name__}")
                            if hasattr(var_data, 'dtype'):
                                print(f"      数据类型: {var_data.dtype}")
                    else:
                        # 其他类型
                        print(f"    变量 {var_name}: 类型 {type(var_data).__name__}")
                except Exception as e:
                    print(f"    变量 {var_name}: 处理统计信息时出错 - {e}")
    
    # 可视化示例
    print("\n生成变量可视化...")
    for var_name in ['u10', 'v10', 'swh', 'water_u', 'water_v']:
        try:
            extractor.visualize_variable(var_name, save_path=f"{var_name}_visualization.png")
        except Exception as e:
            print(f"可视化 {var_name} 失败: {e}")

if __name__ == "__main__":
    main()