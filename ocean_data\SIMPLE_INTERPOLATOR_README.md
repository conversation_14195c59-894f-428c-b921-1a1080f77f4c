# 简化版海洋数据插值器

## 概述

简化版海洋数据插值器是一个轻量级工具，用于对海洋数据进行双线性插值。与原始的`ocean_interpolator.py`相比，它具有以下优势：

1. **简单直接**：只需传入经纬度和数据即可进行插值，不需要加载数据文件
2. **无状态设计**：所有函数都是纯函数，不依赖于类实例状态
3. **高性能**：保持了原始插值器的高性能（约4万点/秒）
4. **轻量级**：没有复杂的依赖，只需要NumPy
5. **灵活性**：可以轻松集成到现有项目中，不需要修改数据加载逻辑

## 主要功能

简化版插值器提供以下核心功能：

1. **双线性插值**：`bilinear_interpolate()` - 对2D数据进行双线性插值
2. **变量值获取**：`get_value()` - 获取指定位置的变量值，支持有效范围检查
3. **风速风向计算**：`get_wind_speed_direction()` - 根据风速分量计算风速和风向
4. **洋流速度方向计算**：`get_current_speed_direction()` - 根据洋流分量计算洋流速度和方向

## 使用方法

### 基本用法

```python
import numpy as np
from simple_interpolator import get_value

# 准备数据
lats = np.array([30.0, 31.0, 32.0])  # 纬度数组
lons = np.array([120.0, 121.0, 122.0])  # 经度数组
data = np.array([  # 2D数据数组
    [1.0, 2.0, 3.0],
    [4.0, 5.0, 6.0],
    [7.0, 8.0, 9.0]
])

# 进行插值
lat, lon = 30.5, 121.5  # 目标位置
value = get_value(data, lats, lons, lat, lon)
print(f"位置 ({lat}, {lon}) 的插值结果: {value}")
```

### 处理风速和风向

```python
from simple_interpolator import get_value, get_wind_speed_direction

# 获取风速分量
u10 = get_value(u10_data, lats, lons, lat, lon, lat_ascending)
v10 = get_value(v10_data, lats, lons, lat, lon, lat_ascending)

# 计算风速和风向
wind_speed, wind_dir = get_wind_speed_direction(u10, v10)
print(f"风速: {wind_speed} m/s, 风向: {wind_dir}°")
```

### 处理洋流速度和方向

```python
from simple_interpolator import get_value, get_current_speed_direction

# 获取洋流分量
water_u = get_value(water_u_data, lats, lons, lat, lon, lat_ascending)
water_v = get_value(water_v_data, lats, lons, lat, lon, lat_ascending)

# 计算洋流速度和方向
current_speed, current_dir = get_current_speed_direction(water_u, water_v)
print(f"洋流速度: {current_speed} m/s, 洋流方向: {current_dir}°")
```

### 处理NaN值

简化版插值器会保留NaN值，如果四个插值点中有任何一个是NaN，则返回NaN。这对于碰撞检测等应用场景非常有用。

```python
# 包含NaN的数据
data_with_nan = np.array([
    [1.0, 2.0, np.nan],
    [4.0, 5.0, 6.0],
    [7.0, np.nan, 9.0]
])

# 在NaN点附近插值
lat, lon = 30.0, 122.0  # NaN点位置
value = get_value(data_with_nan, lats, lons, lat, lon)
print(f"NaN点位置 ({lat}, {lon}) 的插值结果: {value}")  # 将返回NaN
```

## 与原始插值器的区别

1. **数据加载**：简化版不负责加载数据，用户需要自己提供数据数组和坐标数组
2. **状态管理**：简化版没有状态，所有函数都是纯函数，每次调用都需要传入完整的数据
3. **API设计**：简化版API更加直观，函数参数明确表示所需的数据
4. **依赖关系**：简化版只依赖于NumPy，没有其他依赖

## 示例脚本

项目中提供了两个示例脚本：

1. `simple_interpolator.py`：包含简化版插值器的核心功能和简单示例
2. `simple_interpolator_example.py`：展示如何使用简化版插值器处理实际的海洋数据

运行示例脚本：

```bash
python simple_interpolator.py  # 运行简单示例
python simple_interpolator_example.py  # 运行完整示例
```

## 性能

简化版插值器保持了原始插值器的高性能，在测试中可以达到约4万点/秒的插值速度。

## 注意事项

1. 确保提供的纬度和经度数组是有序的（递增或递减）
2. 对于纬度递减的数据，需要设置`lat_ascending=False`
3. 处理NaN值时，如果四个插值点中有任何一个是NaN，则返回NaN