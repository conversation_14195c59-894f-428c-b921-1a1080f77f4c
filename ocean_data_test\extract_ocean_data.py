#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋气象要素数据提取脚本
用于从不同分辨率的NetCDF文件中提取指定经纬度范围的数据
并将其保存为可用于插值的张量格式
"""

import numpy as np
import xarray as xr
import os
import pickle
from typing import Dict, Tuple, Optional, List
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class OceanDataExtractor:
    """
    海洋数据提取器
    支持从不同分辨率的NetCDF文件中提取指定区域的数据
    """
    
    def __init__(self, data_dir: str = "./ocean_data"):
        """
        初始化数据提取器
        
        Args:
            data_dir: 数据文件目录
        """
        self.data_dir = data_dir
        self.extracted_data = {}
        
        # 默认提取范围
        self.default_lon_range = (116, 136)
        self.default_lat_range = (20, 40)
        
    def load_and_extract_data(self, 
                            lon_range: Tuple[float, float] = None,
                            lat_range: Tuple[float, float] = None,
                            time_index: int = 0) -> Dict:
        """
        加载并提取指定范围的海洋数据
        
        Args:
            lon_range: 经度范围 (min_lon, max_lon)
            lat_range: 纬度范围 (min_lat, max_lat)
            time_index: 时间索引
            
        Returns:
            提取的数据字典
        """
        if lon_range is None:
            lon_range = self.default_lon_range
        if lat_range is None:
            lat_range = self.default_lat_range
            
        print(f"开始提取数据...")
        print(f"经度范围: {lon_range[0]}° - {lon_range[1]}°")
        print(f"纬度范围: {lat_range[0]}° - {lat_range[1]}°")
        print(f"时间索引: {time_index}")
        print("="*50)
        
        # 查找数据文件
        data_files = self._find_data_files()
        
        extracted_data = {}
        
        for data_type, file_path in data_files.items():
            print(f"\n处理 {data_type} 数据: {os.path.basename(file_path)}")
            try:
                data = self._extract_single_file(file_path, lon_range, lat_range, time_index)
                if data is not None:
                    extracted_data[data_type] = data
                    print(f"✓ {data_type} 数据提取成功")
                else:
                    print(f"✗ {data_type} 数据提取失败")
            except Exception as e:
                print(f"✗ {data_type} 数据处理出错: {e}")
        
        self.extracted_data = extracted_data
        return extracted_data
    
    def _find_data_files(self) -> Dict[str, str]:
        """
        查找数据文件
        
        Returns:
            数据文件字典 {数据类型: 文件路径}
        """
        data_files = {}
        
        # 定义文件匹配模式
        file_patterns = {
            'wind': ['wind_20180101.nc', 'wind.nc'],
            'wave': ['wave_20180101.nc', 'wave.nc'],
            'current': ['current_sfc_u_2018010100_t003.nc', 'current.nc']
        }
        # 找到存在的第一个文件 就返回
        for data_type, patterns in file_patterns.items():
            for pattern in patterns:
                file_path = os.path.join(self.data_dir, pattern)
                if os.path.exists(file_path):
                    data_files[data_type] = file_path
                    break
        
        print(f"找到 {len(data_files)} 个数据文件:")
        for data_type, file_path in data_files.items():
            print(f"  - {data_type}: {os.path.basename(file_path)}")
        
        return data_files
    
    def _extract_single_file(self, 
                           file_path: str, 
                           lon_range: Tuple[float, float],
                           lat_range: Tuple[float, float],
                           time_index: int) -> Optional[Dict]:
        """
        从单个NetCDF文件中提取数据
        
        Args:
            file_path: 文件路径
            lon_range: 经度范围
            lat_range: 纬度范围
            time_index: 时间索引
            
        Returns:
            提取的数据字典
        """
        try:
            # 加载数据 - 对于current数据文件，需要禁用时间解码以避免tau变量的时间单位问题
            if 'current' in os.path.basename(file_path).lower():
                ds = xr.open_dataset(file_path, decode_times=False)
                print(f"  注意: 对于current数据，已禁用时间解码以避免tau变量的时间单位问题")
            else:
                ds = xr.open_dataset(file_path)
            
            # 获取坐标信息
            coord_info = self._get_coordinate_info(ds)
            if coord_info is None:
                return None
            
            lat_name, lon_name = coord_info
            
            # 获取坐标数组
            lats = ds.coords[lat_name].values
            lons = ds.coords[lon_name].values
            
            print(f"  原始数据范围: 经度 {lons.min():.2f}°-{lons.max():.2f}°, 纬度 {lats.min():.2f}°-{lats.max():.2f}°")
            print(f"  原始数据分辨率: {len(lats)} x {len(lons)}")
            
            # 处理经度范围（考虑跨越180度的情况），lon_mask取值为布尔数组，满足条件的点为True，不满足的点为False
            lon_mask = self._create_longitude_mask(lons, lon_range)
            lat_mask = (lats >= lat_range[0]) & (lats <= lat_range[1])
            
            # 提取区域数据
            extracted_lats = lats[lat_mask]
            extracted_lons = lons[lon_mask]
            
            if len(extracted_lats) == 0 or len(extracted_lons) == 0:
                print(f"  警告: 指定范围内没有数据点")
                return None
            
            print(f"  提取后数据范围: 经度 {extracted_lons.min():.2f}°-{extracted_lons.max():.2f}°, 纬度 {extracted_lats.min():.2f}°-{extracted_lats.max():.2f}°")
            print(f"  提取后数据分辨率: {len(extracted_lats)} x {len(extracted_lons)}")
            
            # 提取所有数据变量
            data_vars = {}
            for var_name in ds.data_vars:
                var_data = ds[var_name]
                
                # 处理不同维度的数据
                if len(var_data.dims) == 3:  # 包含时间维度
                    if 'time' in var_data.dims:
                        # 选择时间切片
                        if time_index < var_data.sizes['time']:
                            var_data = var_data.isel(time=time_index)
                        else:
                            print(f"    警告: 时间索引 {time_index} 超出范围，使用索引 0")
                            var_data = var_data.isel(time=0)
                
                # 提取区域数据
                if lat_name in var_data.dims and lon_name in var_data.dims:
                    # 创建选择器 ，使用where函数，返回满足条件的索引
                    # np.where() 函数返回的是一个元组（tuple），其中第一个元素是满足条件的索引数组。
                    lat_indices = np.where(lat_mask)[0]
                    lon_indices = np.where(lon_mask)[0]
                    
                    # 使用isel进行索引选择
                    var_extracted = var_data.isel({lat_name: lat_indices, lon_name: lon_indices})
                    
                    # 转换为numpy数组
                    data_array = var_extracted.values
                    
                    # 检查数据有效性 存入一坨字典。
                    if not np.isnan(data_array).all():
                        data_vars[var_name] = {
                            'data': data_array,
                            'shape': data_array.shape,
                            'dtype': str(data_array.dtype),
                            'valid_points': np.sum(~np.isnan(data_array)),
                            'total_points': data_array.size
                        }
                        print(f"    变量 {var_name}: 形状 {data_array.shape}, 有效点 {np.sum(~np.isnan(data_array))}/{data_array.size}")
            
            # 构建返回数据
            result = {
                'coordinates': {
                    'latitude': extracted_lats,
                    'longitude': extracted_lons,
                    'lat_name': lat_name,
                    'lon_name': lon_name
                },
                'variables': data_vars,
                'metadata': {
                    'source_file': os.path.basename(file_path),
                    'extraction_time': datetime.now().isoformat(),
                    'lon_range': lon_range,
                    'lat_range': lat_range,
                    'time_index': time_index,
                    'original_shape': (len(lats), len(lons)),
                    'extracted_shape': (len(extracted_lats), len(extracted_lons))
                }
            }
            
            ds.close()
            return result
            
        except Exception as e:
            print(f"  错误: {e}")
            return None
    
    def _get_coordinate_info(self, ds: xr.Dataset) -> Optional[Tuple[str, str]]:
        """
        获取坐标信息
        
        Args:
            ds: xarray数据集
            
        Returns:
            (纬度坐标名, 经度坐标名) 或 None
        """
        # 常见的坐标名称
        lat_names = ['lat', 'latitude', 'LAT', 'LATITUDE']
        lon_names = ['lon', 'longitude', 'LON', 'LONGITUDE']
        
        lat_name = None
        lon_name = None
        
        # 查找纬度坐标
        for name in lat_names:
            if name in ds.coords:
                lat_name = name
                break
        
        # 查找经度坐标
        for name in lon_names:
            if name in ds.coords:
                lon_name = name
                break
        
        if lat_name is None or lon_name is None:
            print(f"  错误: 无法找到坐标信息")
            print(f"  可用坐标: {list(ds.coords.keys())}")
            return None
        
        return lat_name, lon_name
    
    def _create_longitude_mask(self, lons: np.ndarray, lon_range: Tuple[float, float]) -> np.ndarray:
        """
        创建经度掩码，处理跨越180度的情况
        
        Args:
            lons: 经度数组
            lon_range: 经度范围
            
        Returns:
            布尔掩码数组
        """
        min_lon, max_lon = lon_range
        
        # 标准化经度到0-360度
        lons_normalized = np.where(lons < 0, lons + 360, lons)
        min_lon_norm = min_lon if min_lon >= 0 else min_lon + 360
        max_lon_norm = max_lon if max_lon >= 0 else max_lon + 360
        
        if min_lon_norm <= max_lon_norm:
            # 不跨越180度
            mask = (lons_normalized >= min_lon_norm) & (lons_normalized <= max_lon_norm)
        else:
            # 跨越180度
            mask = (lons_normalized >= min_lon_norm) | (lons_normalized <= max_lon_norm)
        
        return mask
    
    def save_extracted_data(self, output_path: str = "extracted_ocean_data.pkl") -> bool:
        """
        保存提取的数据为pickle格式
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否保存成功
        """
        if not self.extracted_data:
            print("没有可保存的数据")
            return False
        
        try:
            with open(output_path, 'wb') as f:
                pickle.dump(self.extracted_data, f)
            
            print(f"\n数据已保存到: {output_path}")
            print(f"文件大小: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
            return True
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def save_as_numpy(self, output_dir: str = "extracted_tensors") -> bool:
        """
        将提取的数据保存为numpy张量格式
        
        Args:
            output_dir: 输出目录
            
        Returns:
            是否保存成功
        """
        if not self.extracted_data:
            print("没有可保存的数据")
            return False
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            for data_type, data_info in self.extracted_data.items():
                type_dir = os.path.join(output_dir, data_type)
                os.makedirs(type_dir, exist_ok=True)
                
                # 保存坐标
                coords = data_info['coordinates']
                np.save(os.path.join(type_dir, 'latitude.npy'), coords['latitude'])
                np.save(os.path.join(type_dir, 'longitude.npy'), coords['longitude'])
                
                # 保存变量数据
                for var_name, var_info in data_info['variables'].items():
                    np.save(os.path.join(type_dir, f'{var_name}.npy'), var_info['data'])
                
                # 保存元数据
                metadata_file = os.path.join(type_dir, 'metadata.txt')
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    f.write(f"数据类型: {data_type}\n")
                    f.write(f"源文件: {data_info['metadata']['source_file']}\n")
                    f.write(f"提取时间: {data_info['metadata']['extraction_time']}\n")
                    f.write(f"经度范围: {data_info['metadata']['lon_range']}\n")
                    f.write(f"纬度范围: {data_info['metadata']['lat_range']}\n")
                    f.write(f"原始形状: {data_info['metadata']['original_shape']}\n")
                    f.write(f"提取后形状: {data_info['metadata']['extracted_shape']}\n")
                    f.write(f"\n变量信息:\n")
                    for var_name, var_info in data_info['variables'].items():
                        f.write(f"  {var_name}: 形状 {var_info['shape']}, 类型 {var_info['dtype']}, 有效点 {var_info['valid_points']}/{var_info['total_points']}\n")
            
            print(f"\n张量数据已保存到目录: {output_dir}")
            return True
            
        except Exception as e:
            print(f"保存张量数据失败: {e}")
            return False
    
    def print_summary(self):
        """
        打印数据提取摘要
        """
        if not self.extracted_data:
            print("没有提取的数据")
            return
        
        print("\n" + "="*60)
        print("数据提取摘要")
        print("="*60)
        
        for data_type, data_info in self.extracted_data.items():
            print(f"\n{data_type.upper()} 数据:")
            print(f"  源文件: {data_info['metadata']['source_file']}")
            print(f"  坐标范围: 经度 {data_info['coordinates']['longitude'].min():.2f}°-{data_info['coordinates']['longitude'].max():.2f}°")
            print(f"           纬度 {data_info['coordinates']['latitude'].min():.2f}°-{data_info['coordinates']['latitude'].max():.2f}°")
            print(f"  数据分辨率: {len(data_info['coordinates']['latitude'])} x {len(data_info['coordinates']['longitude'])}")
            print(f"  变量数量: {len(data_info['variables'])}")
            
            for var_name, var_info in data_info['variables'].items():
                print(f"    - {var_name}: {var_info['shape']}, 有效率 {var_info['valid_points']/var_info['total_points']*100:.1f}%")

def main():
    """
    主函数 - 演示数据提取功能
    """
    print("海洋气象要素数据提取工具")
    print("="*50)
    
    # 创建提取器
    extractor = OceanDataExtractor()
    
    # 设置提取参数
    lon_range = (116, 136)  # 经度范围
    lat_range = (20, 40)    # 纬度范围
    time_index = 0          # 时间索引
    
    print(f"提取参数:")
    print(f"  经度范围: {lon_range[0]}° - {lon_range[1]}°")
    print(f"  纬度范围: {lat_range[0]}° - {lat_range[1]}°")
    print(f"  时间索引: {time_index}")
    
    # 提取数据
    extracted_data = extractor.load_and_extract_data(
        lon_range=lon_range,
        lat_range=lat_range,
        time_index=time_index
    )
    
    if extracted_data:
        # 打印摘要
        extractor.print_summary()
        
        # 保存数据
        print("\n保存数据...")
        extractor.save_extracted_data("extracted_ocean_data.pkl")
        extractor.save_as_numpy("extracted_tensors")
        
        print("\n数据提取完成！")
        print("\n使用说明:")
        print("1. extracted_ocean_data.pkl - 完整的Python对象，可用pickle加载")
        print("2. extracted_tensors/ - 分类保存的numpy张量，便于直接使用")
        print("3. 每个数据类型包含坐标文件和变量文件")
        print("4. 可直接用于插值和机器学习模型")
    else:
        print("\n数据提取失败，请检查数据文件和参数设置")

if __name__ == "__main__":
    main()