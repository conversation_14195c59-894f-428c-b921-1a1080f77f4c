import numpy as np
import time
import sys
import os

# 添加当前目录到路径，以便导入模块
sys.path.append(os.getcwd())

from environment import PathPlanningEnv

# 定义原始的雷达计算函数
def original_get_radar_readings(env):
    """获取36维雷达读数 - 原始版本"""
    radar_readings = []
    
    for angle in env.radar_angles:
        # 计算雷达射线方向
        global_angle = env.robot_angle + angle
        ray_dir = np.array([np.cos(global_angle), np.sin(global_angle)])
        
        min_distance = env.radar_range
        
        # 检测边界
        t_vals = []
        if ray_dir[0] != 0:
            t_vals.extend([
                -env.robot_pos[0] / ray_dir[0],  # 左边界
                (env.map_size - env.robot_pos[0]) / ray_dir[0]  # 右边界
            ])
        if ray_dir[1] != 0:
            t_vals.extend([
                -env.robot_pos[1] / ray_dir[1],  # 下边界
                (env.map_size - env.robot_pos[1]) / ray_dir[1]  # 上边界
            ])
        
        for t in t_vals:
            if t > 0:
                intersection = env.robot_pos + t * ray_dir
                if (0 <= intersection[0] <= env.map_size and 
                    0 <= intersection[1] <= env.map_size):
                    distance = min(t, env.radar_range)
                    min_distance = min(min_distance, distance)
        
        # 检测障碍物
        for obs in env.obstacles:
            obs_center = np.array([obs['x'], obs['y']])
            
            # 射线与圆的交点计算
            to_obs = obs_center - env.robot_pos
            proj_length = np.dot(to_obs, ray_dir)
            
            if proj_length > 0:
                closest_point = env.robot_pos + proj_length * ray_dir
                dist_to_center = np.linalg.norm(closest_point - obs_center)
                
                if dist_to_center <= obs['radius']:
                    # 计算交点距离
                    chord_half = np.sqrt(obs['radius']**2 - dist_to_center**2)
                    intersection_dist = proj_length - chord_half
                    
                    if intersection_dist > 0:
                        min_distance = min(min_distance, intersection_dist)
        
        radar_readings.append(min_distance)
    
    return np.array(radar_readings, dtype=np.float32)

# 创建环境
env = PathPlanningEnv(map_size=100, num_obstacles=13, robot_radius=2.5)

# 重置环境
env.reset()

# 测试原始雷达计算性能
num_tests = 1000
print("测试原始雷达计算性能...")
start_time = time.time()

for _ in range(num_tests):
    radar_readings_original = original_get_radar_readings(env)

end_time = time.time()
original_time = end_time - start_time
print(f"原始版本总耗时: {original_time:.4f} 秒")
print(f"原始版本平均每次耗时: {original_time / num_tests * 1000:.4f} 毫秒")

# 测试优化后的雷达计算性能
print("\n测试优化后的雷达计算性能...")
start_time = time.time()

for _ in range(num_tests):
    radar_readings_optimized = env._get_radar_readings()

end_time = time.time()
optimized_time = end_time - start_time
print(f"优化版本总耗时: {optimized_time:.4f} 秒")
print(f"优化版本平均每次耗时: {optimized_time / num_tests * 1000:.4f} 毫秒")

# 计算性能提升
speedup = original_time / optimized_time
print(f"\n性能提升: {speedup:.2f}倍")

# 验证结果一致性
print("\n验证结果一致性...")
radar_readings_original = original_get_radar_readings(env)
radar_readings_optimized = env._get_radar_readings()

max_diff = np.max(np.abs(radar_readings_original - radar_readings_optimized))
print(f"最大差异: {max_diff:.8f}")

if max_diff < 1e-5:
    print("结果一致，优化成功！")
else:
    print("警告：结果不一致，请检查优化代码！")