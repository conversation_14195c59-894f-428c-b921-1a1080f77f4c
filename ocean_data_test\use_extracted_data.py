#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取数据使用示例
展示如何加载和使用提取的海洋气象张量数据进行插值
"""

import numpy as np
import pickle
import os
import matplotlib.pyplot as plt
from ocean_data_loader import BilinearInterpolator
from typing import Dict, Tuple, Optional

class ExtractedDataLoader:
    """
    提取数据加载器
    用于加载和使用提取的海洋气象张量数据
    """
    
    def __init__(self, tensor_dir: str = "extracted_tensors"):
        """
        初始化数据加载器
        
        Args:
            tensor_dir: 张量数据目录
        """
        self.tensor_dir = tensor_dir
        self.data = {}
        self.interpolators = {}
        
    def load_tensor_data(self) -> bool:
        """
        加载张量数据
        
        Returns:
            是否加载成功
        """
        if not os.path.exists(self.tensor_dir):
            print(f"张量数据目录不存在: {self.tensor_dir}")
            return False
        
        print("加载提取的张量数据...")
        print("="*40)
        
        # 遍历数据类型目录
        for data_type in os.listdir(self.tensor_dir):
            type_dir = os.path.join(self.tensor_dir, data_type)
            if not os.path.isdir(type_dir):
                continue
            
            print(f"\n加载 {data_type} 数据...")
            
            try:
                # 加载坐标
                lat_file = os.path.join(type_dir, 'latitude.npy')
                lon_file = os.path.join(type_dir, 'longitude.npy')
                
                if not (os.path.exists(lat_file) and os.path.exists(lon_file)):
                    print(f"  错误: 缺少坐标文件")
                    continue
                
                lats = np.load(lat_file)
                lons = np.load(lon_file)
                
                print(f"  坐标范围: 经度 {lons.min():.2f}°-{lons.max():.2f}°, 纬度 {lats.min():.2f}°-{lats.max():.2f}°")
                print(f"  数据分辨率: {len(lats)} x {len(lons)}")
                
                # 加载变量数据
                variables = {}
                for file_name in os.listdir(type_dir):
                    if file_name.endswith('.npy') and file_name not in ['latitude.npy', 'longitude.npy']:
                        var_name = file_name[:-4]  # 移除.npy扩展名
                        var_file = os.path.join(type_dir, file_name)
                        var_data = np.load(var_file)
                        variables[var_name] = var_data
                        
                        valid_points = np.sum(~np.isnan(var_data))
                        total_points = var_data.size
                        print(f"    变量 {var_name}: 形状 {var_data.shape}, 有效率 {valid_points/total_points*100:.1f}%")
                
                # 存储数据
                self.data[data_type] = {
                    'coordinates': {
                        'latitude': lats,
                        'longitude': lons
                    },
                    'variables': variables
                }
                
                print(f"  ✓ {data_type} 数据加载成功，包含 {len(variables)} 个变量")
                
            except Exception as e:
                print(f"  ✗ {data_type} 数据加载失败: {e}")
        
        print(f"\n总共加载了 {len(self.data)} 种数据类型")
        return len(self.data) > 0
    
    def setup_interpolators(self):
        """
        设置插值器
        """
        print("\n设置插值器...")
        
        for data_type, data_info in self.data.items():
            print(f"  设置 {data_type} 插值器...")
            
            lats = data_info['coordinates']['latitude']
            lons = data_info['coordinates']['longitude']
            
            self.interpolators[data_type] = {}
            
            for var_name, var_data in data_info['variables'].items():
                try:
                    # 创建插值器
                    interpolator = BilinearInterpolator(
                        (lats, lons), 
                        var_data, 
                        fill_value=np.nan
                    )
                    self.interpolators[data_type][var_name] = interpolator
                    print(f"    ✓ {var_name} 插值器创建成功")
                    
                except Exception as e:
                    print(f"    ✗ {var_name} 插值器创建失败: {e}")
    
    def interpolate_at_points(self, points: np.ndarray) -> Dict:
        """
        在指定点进行插值
        
        Args:
            points: 查询点数组，形状为 (N, 2)，每行为 [lat, lon]
            
        Returns:
            插值结果字典
        """
        if not self.interpolators:
            print("请先设置插值器")
            return {}
        
        results = {}
        
        for data_type, type_interpolators in self.interpolators.items():
            results[data_type] = {}
            
            for var_name, interpolator in type_interpolators.items():
                try:
                    values = interpolator(points)
                    results[data_type][var_name] = values
                except Exception as e:
                    print(f"插值失败 {data_type}.{var_name}: {e}")
                    results[data_type][var_name] = np.full(len(points), np.nan)
        
        return results
    
    def extract_grid_around_point(self, center_lat: float, center_lon: float, 
                                grid_size: float = 0.5, grid_points: int = 5) -> Dict:
        """
        在指定点周围提取网格数据
        
        Args:
            center_lat: 中心纬度
            center_lon: 中心经度
            grid_size: 网格大小（度）
            grid_points: 网格点数
            
        Returns:
            网格数据字典
        """
        # 生成网格点
        half_size = grid_size / 2
        lat_offsets = np.linspace(-half_size, half_size, grid_points)
        lon_offsets = np.linspace(-half_size, half_size, grid_points)
        
        lat_grid, lon_grid = np.meshgrid(lat_offsets, lon_offsets, indexing='ij')
        
        # 计算实际坐标
        grid_lats = center_lat + lat_grid.flatten()
        grid_lons = center_lon + lon_grid.flatten()
        
        # 创建查询点数组
        query_points = np.column_stack([grid_lats, grid_lons])
        
        # 执行插值
        interpolated_data = self.interpolate_at_points(query_points)
        
        # 重新整形为网格
        grid_data = {}
        for data_type, type_data in interpolated_data.items():
            grid_data[data_type] = {}
            for var_name, values in type_data.items():
                grid_data[data_type][var_name] = values.reshape(grid_points, grid_points)
        
        return {
            'grid_data': grid_data,
            'coordinates': {
                'center': (center_lat, center_lon),
                'lat_grid': center_lat + lat_grid,
                'lon_grid': center_lon + lon_grid,
                'grid_size': grid_size,
                'grid_points': grid_points
            }
        }
    
    def visualize_data(self, data_type: str = None, var_name: str = None, 
                      save_path: str = None):
        """
        可视化数据
        
        Args:
            data_type: 数据类型
            var_name: 变量名
            save_path: 保存路径
        """
        if not self.data:
            print("没有加载的数据")
            return
        
        # 如果没有指定，使用第一个可用的数据
        if data_type is None:
            data_type = list(self.data.keys())[0]
        
        if data_type not in self.data:
            print(f"数据类型 {data_type} 不存在")
            return
        
        if var_name is None:
            var_name = list(self.data[data_type]['variables'].keys())[0]
        
        if var_name not in self.data[data_type]['variables']:
            print(f"变量 {var_name} 不存在于 {data_type} 数据中")
            return
        
        # 获取数据
        lats = self.data[data_type]['coordinates']['latitude']
        lons = self.data[data_type]['coordinates']['longitude']
        data = self.data[data_type]['variables'][var_name]
        
        # 创建图形
        plt.figure(figsize=(12, 8))
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制热力图
        plt.contourf(lon_grid, lat_grid, data, levels=20, cmap='viridis')
        plt.colorbar(label=f'{var_name}')
        
        plt.xlabel('经度 (°)')
        plt.ylabel('纬度 (°)')
        plt.title(f'{data_type.upper()} - {var_name} 数据分布')
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图像已保存到: {save_path}")
        
        plt.show()
    
    def print_data_summary(self):
        """
        打印数据摘要
        """
        if not self.data:
            print("没有加载的数据")
            return
        
        print("\n" + "="*50)
        print("提取数据摘要")
        print("="*50)
        
        for data_type, data_info in self.data.items():
            print(f"\n{data_type.upper()} 数据:")
            
            coords = data_info['coordinates']
            print(f"  坐标范围: 经度 {coords['longitude'].min():.2f}°-{coords['longitude'].max():.2f}°")
            print(f"           纬度 {coords['latitude'].min():.2f}°-{coords['latitude'].max():.2f}°")
            print(f"  数据分辨率: {len(coords['latitude'])} x {len(coords['longitude'])}")
            print(f"  变量列表:")
            
            for var_name, var_data in data_info['variables'].items():
                valid_points = np.sum(~np.isnan(var_data))
                total_points = var_data.size
                print(f"    - {var_name}: 形状 {var_data.shape}, 有效率 {valid_points/total_points*100:.1f}%")

def demo_usage():
    """
    演示使用方法
    """
    print("提取数据使用演示")
    print("="*40)
    
    # 创建加载器
    loader = ExtractedDataLoader()
    
    # 加载数据
    if not loader.load_tensor_data():
        print("数据加载失败")
        return
    
    # 打印摘要
    loader.print_data_summary()
    
    # 设置插值器
    loader.setup_interpolators()
    
    # 演示单点插值
    print("\n" + "="*40)
    print("单点插值演示")
    print("="*40)
    
    test_points = np.array([
        [25.0, 120.0],  # 台湾海峡
        [30.0, 125.0],  # 东海
        [35.0, 130.0],  # 日本海
    ])
    
    results = loader.interpolate_at_points(test_points)
    
    for i, (lat, lon) in enumerate(test_points):
        print(f"\n位置 ({lat:.1f}°, {lon:.1f}°):")
        for data_type, type_data in results.items():
            print(f"  {data_type}:")
            for var_name, values in type_data.items():
                print(f"    {var_name}: {values[i]:.3f}")
    
    # 演示网格提取
    print("\n" + "="*40)
    print("网格数据提取演示")
    print("="*40)
    
    center_lat, center_lon = 30.0, 125.0
    grid_result = loader.extract_grid_around_point(
        center_lat, center_lon, 
        grid_size=2.0, 
        grid_points=5
    )
    
    print(f"中心点: ({center_lat}°, {center_lon}°)")
    print(f"网格大小: 2.0° x 2.0°")
    print(f"网格点数: 5 x 5")
    
    for data_type, type_data in grid_result['grid_data'].items():
        print(f"\n{data_type} 网格数据:")
        for var_name, grid_data in type_data.items():
            print(f"  {var_name} 范围: {np.nanmin(grid_data):.3f} - {np.nanmax(grid_data):.3f}")
    
    # 可视化（如果有数据）
    if loader.data:
        print("\n生成可视化图像...")
        try:
            loader.visualize_data(save_path="extracted_data_visualization.png")
        except Exception as e:
            print(f"可视化失败: {e}")
    
    print("\n演示完成！")

def load_pickle_data(pickle_file: str = "extracted_ocean_data.pkl"):
    """
    加载pickle格式的数据
    
    Args:
        pickle_file: pickle文件路径
    """
    if not os.path.exists(pickle_file):
        print(f"Pickle文件不存在: {pickle_file}")
        return None
    
    try:
        with open(pickle_file, 'rb') as f:
            data = pickle.load(f)
        
        print(f"从 {pickle_file} 加载数据成功")
        print(f"数据类型: {list(data.keys())}")
        
        return data
        
    except Exception as e:
        print(f"加载pickle数据失败: {e}")
        return None

if __name__ == "__main__":
    # 运行演示
    demo_usage()
    
    # 也可以加载pickle数据
    print("\n" + "="*50)
    print("Pickle数据加载演示")
    print("="*50)
    pickle_data = load_pickle_data()
    if pickle_data:
        print("Pickle数据结构:")
        for data_type, data_info in pickle_data.items():
            print(f"  {data_type}: {len(data_info['variables'])} 个变量")