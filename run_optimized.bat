@echo off
echo 正在启动优化版交互式路径规划系统...
echo.

REM 激活py311环境
call conda activate py311

REM 检查环境是否激活成功
if %errorlevel% neq 0 (
    echo 错误：无法激活py311环境
    echo 请确保已安装Anaconda/Miniconda并创建了py311环境
    pause
    exit /b 1
)

echo ✓ py311环境已激活

REM 检查并安装必要的依赖
echo 检查依赖模块...
python -c "import stable_baselines3" 2>nul
if %errorlevel% neq 0 (
    echo 正在安装stable-baselines3...
    pip install stable-baselines3
    if %errorlevel% neq 0 (
        echo 错误：安装stable-baselines3失败
        pause
        exit /b 1
    )
)

echo ✓ 依赖检查完成

REM 运行优化版脚本
echo 启动优化版路径规划系统...
python interactive_path_planning_up.py

REM 如果出现错误，暂停以查看错误信息
if %errorlevel% neq 0 (
    echo.
    echo 程序运行出现错误，请检查上述错误信息
    pause
)
