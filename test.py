import os
import numpy as np
import matplotlib.pyplot as plt
import time
from stable_baselines3 import TD3
from stable_baselines3.common.vec_env import DummyVecEnv

from environment import PathPlanningEnv
from networks import RadarProcessor

class PathPlanningTester:
    """路径规划测试器"""
    
    def __init__(self, model_path, num_obstacles=24):
        """初始化测试器"""
        self.model_path = model_path
        self.model = None
        self.env = None
        self.num_obstacles = num_obstacles
        self.load_model()
    
    def load_model(self):
        """加载训练好的模型"""
        try:
            self.model = TD3.load(self.model_path)
            print(f"成功加载模型: {self.model_path}")
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
        return True
    
    def create_test_env(self, render_mode=None):
        """创建测试环境"""
        self.env = PathPlanningEnv(
            map_size=100,
            num_obstacles=self.num_obstacles,
            robot_radius=0.5,
            render_mode=render_mode
        )
        return self.env
    
    def single_episode_test(self, render=False, deterministic=True, max_steps=1000):
        """单回合测试"""
        if self.model is None:
            print("模型未加载！")
            return None
        
        env = self.create_test_env(render_mode='human' if render else None)
        obs, _ = env.reset()
        
        total_reward = 0
        steps = 0
        trajectory = []
        done = False
        
        start_pos = env.start_pos.copy()
        target_pos = env.target_pos.copy()
        
        print(f"测试开始:")
        print(f"起点: ({start_pos[0]:.1f}, {start_pos[1]:.1f})")
        print(f"终点: ({target_pos[0]:.1f}, {target_pos[1]:.1f})")
        print(f"直线距离: {np.linalg.norm(np.array(target_pos) - np.array(start_pos)):.1f}")
        
        while not done and steps < max_steps:
            action, _ = self.model.predict(obs, deterministic=deterministic)
            obs, reward, terminated, truncated, info = env.step(action)
            
            # 记录轨迹
            trajectory.append({
                'position': env.robot_pos.copy(),
                'action': action.copy(),
                'reward': reward,
                'distance_to_target': info.get('distance_to_target', 0)
            })
            
            total_reward += reward
            steps += 1
            done = terminated or truncated
            
            if render:
                env.render()
                time.sleep(0.05)  # 控制渲染速度
            
            if info.get('success', False):
                print(f"成功到达目标！")
                break
        
        final_distance = info.get('distance_to_target', float('inf'))
        success = info.get('success', False)
        
        print(f"测试完成:")
        print(f"总步数: {steps}")
        print(f"总奖励: {total_reward:.2f}")
        print(f"最终距离: {final_distance:.2f}")
        print(f"成功: {'是' if success else '否'}")
        
        env.close()
        
        return {
            'success': success,
            'steps': steps,
            'total_reward': total_reward,
            'final_distance': final_distance,
            'trajectory': trajectory,
            'start_pos': start_pos,
            'target_pos': target_pos
        }
    
    def batch_test(self, num_tests=30, use_fixed_scenarios=True):
        """批量测试"""
        if self.model is None:
            print("模型未加载！")
            return
        
        print(f"\n开始批量测试 ({num_tests} 次)...")
        print(f"障碍物数量: {self.num_obstacles}")
        print(f"使用固定场景: {'是' if use_fixed_scenarios else '否'}")
        
        env = self.create_test_env()
        
        # 测试统计
        success_count = 0
        total_rewards = []
        total_steps = []
        distances = []
        
        for i in range(num_tests):
            obs, _ = env.reset()
            done = False
            episode_reward = 0
            steps = 0
            
            while not done and steps < 1000:  # 设置最大步数限制
                action, _ = self.model.predict(obs, deterministic=True)
                obs, reward, terminated, truncated, info = env.step(action)
                episode_reward += reward
                steps += 1
                done = terminated or truncated
            
            # 记录结果
            success = info.get('success', False)
            final_distance = info.get('distance_to_target', float('inf'))
            
            if success:
                success_count += 1
            
            total_rewards.append(episode_reward)
            total_steps.append(steps)
            distances.append(final_distance)
            
            # 打印进度
            print(f"测试 {i+1}/{num_tests}: {'成功' if success else '失败'}, 步数: {steps}, 奖励: {episode_reward:.2f}, 距离: {final_distance:.2f}")
        
        # 计算统计数据
        success_rate = success_count / num_tests * 100
        avg_reward = np.mean(total_rewards)
        std_reward = np.std(total_rewards)
        avg_steps = np.mean(total_steps)
        std_steps = np.std(total_steps)
        avg_distance = np.mean(distances)
        std_distance = np.std(distances)
        
        # 打印结果
        print("\n批量测试结果:")
        print(f"成功率: {success_rate:.2f}% ({success_count}/{num_tests})")
        print(f"平均奖励: {avg_reward:.2f} ± {std_reward:.2f}")
        print(f"平均步数: {avg_steps:.2f} ± {std_steps:.2f}")
        print(f"平均最终距离: {avg_distance:.2f} ± {std_distance:.2f}")
        
        env.close()
        return {
            'success_rate': success_rate,
            'avg_reward': avg_reward,
            'avg_steps': avg_steps,
            'avg_distance': avg_distance
        }
    
    def analyze_batch_results(self, results):
        """分析批量测试结果"""
        if not results:
            print("无测试结果可分析")
            return
        
        # 基本统计
        success_rate = np.mean([r['success'] for r in results]) * 100
        avg_steps = np.mean([r['steps'] for r in results])
        avg_reward = np.mean([r['total_reward'] for r in results])
        avg_final_distance = np.mean([r['final_distance'] for r in results])
        
        successful_results = [r for r in results if r['success']]
        if successful_results:
            avg_success_steps = np.mean([r['steps'] for r in successful_results])
            avg_success_reward = np.mean([r['total_reward'] for r in successful_results])
        else:
            avg_success_steps = 0
            avg_success_reward = 0
        
        print(f"\n批量测试结果分析:")
        print(f"总测试次数: {len(results)}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均步数: {avg_steps:.1f}")
        print(f"平均奖励: {avg_reward:.2f}")
        print(f"平均最终距离: {avg_final_distance:.2f}")
        
        if successful_results:
            print(f"\n成功案例分析:")
            print(f"成功案例数: {len(successful_results)}")
            print(f"成功时平均步数: {avg_success_steps:.1f}")
            print(f"成功时平均奖励: {avg_success_reward:.2f}")
        
        # 绘制统计图表
        self.plot_batch_results(results)
    
    def plot_batch_results(self, results):
        """绘制批量测试结果图表"""
        if not results:
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 成功率饼图
        success_count = sum([r['success'] for r in results])
        fail_count = len(results) - success_count
        axes[0, 0].pie([success_count, fail_count], 
                      labels=['成功', '失败'], 
                      autopct='%1.1f%%',
                      colors=['green', 'red'])
        axes[0, 0].set_title('成功率')
        
        # 步数分布
        steps = [r['steps'] for r in results]
        axes[0, 1].hist(steps, bins=20, alpha=0.7, color='blue')
        axes[0, 1].set_xlabel('步数')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].set_title('步数分布')
        axes[0, 1].axvline(np.mean(steps), color='red', linestyle='--', label=f'平均值: {np.mean(steps):.1f}')
        axes[0, 1].legend()
        
        # 奖励分布
        rewards = [r['total_reward'] for r in results]
        axes[0, 2].hist(rewards, bins=20, alpha=0.7, color='green')
        axes[0, 2].set_xlabel('总奖励')
        axes[0, 2].set_ylabel('频次')
        axes[0, 2].set_title('奖励分布')
        axes[0, 2].axvline(np.mean(rewards), color='red', linestyle='--', label=f'平均值: {np.mean(rewards):.2f}')
        axes[0, 2].legend()
        
        # 最终距离分布
        final_distances = [r['final_distance'] for r in results]
        axes[1, 0].hist(final_distances, bins=20, alpha=0.7, color='orange')
        axes[1, 0].set_xlabel('最终距离')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('最终距离分布')
        axes[1, 0].axvline(np.mean(final_distances), color='red', linestyle='--', 
                          label=f'平均值: {np.mean(final_distances):.2f}')
        axes[1, 0].legend()
        
        # 成功 vs 失败的步数对比
        success_steps = [r['steps'] for r in results if r['success']]
        fail_steps = [r['steps'] for r in results if not r['success']]
        
        axes[1, 1].boxplot([success_steps, fail_steps], labels=['成功', '失败'])
        axes[1, 1].set_ylabel('步数')
        axes[1, 1].set_title('成功 vs 失败的步数对比')
        
        # 轨迹长度效率分析
        efficiencies = []
        for r in results:
            if r['success']:
                straight_distance = np.linalg.norm(np.array(r['target_pos']) - np.array(r['start_pos']))
                efficiency = straight_distance / (r['steps'] * 0.3)  # 假设每步平均移动0.3单位
                efficiencies.append(efficiency)
        
        if efficiencies:
            axes[1, 2].hist(efficiencies, bins=15, alpha=0.7, color='purple')
            axes[1, 2].set_xlabel('路径效率')
            axes[1, 2].set_ylabel('频次')
            axes[1, 2].set_title('路径效率分布')
            axes[1, 2].axvline(np.mean(efficiencies), color='red', linestyle='--', 
                              label=f'平均值: {np.mean(efficiencies):.3f}')
            axes[1, 2].legend()
        else:
            axes[1, 2].text(0.5, 0.5, '无成功案例', ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].set_title('路径效率分布')
        
        plt.tight_layout()
        plt.savefig('batch_test_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def visualize_trajectory(self, result):
        """可视化单次测试的轨迹"""
        if not result or not result['trajectory']:
            print("无轨迹数据可视化")
            return
        
        trajectory = result['trajectory']
        positions = np.array([t['position'] for t in trajectory])
        
        plt.figure(figsize=(12, 10))
        
        # 绘制地图和障碍物
        for obs in self.env.obstacles:
            circle = plt.Circle((obs['x'], obs['y']), obs['radius'], color='red', alpha=0.7)
            plt.gca().add_patch(circle)
        
        # 绘制轨迹
        plt.plot(positions[:, 0], positions[:, 1], 'b-', linewidth=2, label='机器人轨迹')
        
        # 绘制起点和终点
        plt.plot(result['start_pos'][0], result['start_pos'][1], 'go', markersize=12, label='起点')
        plt.plot(result['target_pos'][0], result['target_pos'][1], 'r*', markersize=15, label='终点')
        
        # 绘制最终位置
        plt.plot(positions[-1, 0], positions[-1, 1], 'bs', markersize=10, label='最终位置')
        
        plt.xlim(0, 100)
        plt.ylim(0, 100)
        plt.xlabel('X 坐标')
        plt.ylabel('Y 坐标')
        plt.title(f'路径规划轨迹 - {"成功" if result["success"] else "失败"}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        
        # 添加性能信息
        info_text = f"步数: {result['steps']}\n奖励: {result['total_reward']:.2f}\n最终距离: {result['final_distance']:.2f}"
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.savefig(f'trajectory_{"success" if result["success"] else "fail"}.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_stress_test_results(self, results, obstacle_counts):
        """绘制压力测试结果"""
        plt.figure(figsize=(15, 5))
        
        # 绘制成功率
        plt.subplot(1, 3, 1)
        success_rates = [results[count]['success_rate'] for count in obstacle_counts]
        plt.plot(obstacle_counts, success_rates, 'o-', linewidth=2)
        plt.title('不同障碍物数量下的成功率')
        plt.xlabel('障碍物数量')
        plt.ylabel('成功率 (%)')
        plt.grid(True)
        
        # 绘制平均奖励
        plt.subplot(1, 3, 2)
        avg_rewards = [results[count]['avg_reward'] for count in obstacle_counts]
        plt.plot(obstacle_counts, avg_rewards, 'o-', linewidth=2)
        plt.title('不同障碍物数量下的平均奖励')
        plt.xlabel('障碍物数量')
        plt.ylabel('平均奖励')
        plt.grid(True)
        
        # 绘制平均步数
        plt.subplot(1, 3, 3)
        avg_steps = [results[count]['avg_steps'] for count in obstacle_counts]
        plt.plot(obstacle_counts, avg_steps, 'o-', linewidth=2)
        plt.title('不同障碍物数量下的平均步数')
        plt.xlabel('障碍物数量')
        plt.ylabel('平均步数')
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()
        
        # 保存图表
        plt.savefig('stress_test_results.png')
        print("压力测试结果图表已保存到 stress_test_results.png")
    
    def stress_test(self):
        """压力测试 - 测试不同障碍物数量下的性能"""
        if self.model is None:
            print("模型未加载！")
            return
        
        print("\n开始压力测试 - 测试不同障碍物数量下的性能...")
        
        # 测试不同障碍物数量
        obstacle_counts = [5, 8, 10, 13, 15]
        num_tests_per_count = 20
        
        results = {}
        
        for count in obstacle_counts:
            print(f"\n测试障碍物数量: {count}")
            
            # 创建特定障碍物数量的环境
            env = PathPlanningEnv(
                map_size=100,
                num_obstacles=count,
                robot_radius=2.5
            )
            
            # 测试统计
            success_count = 0
            total_rewards = []
            total_steps = []
            
            for i in range(num_tests_per_count):
                obs, _ = env.reset()
                done = False
                episode_reward = 0
                steps = 0
                
                while not done and steps < 1000:  # 设置最大步数限制
                    action, _ = self.model.predict(obs, deterministic=True)
                    obs, reward, terminated, truncated, info = env.step(action)
                    episode_reward += reward
                    steps += 1
                    done = terminated or truncated
                    
                    # 记录结果
                    success = info.get('success', False)
                    
                    if success:
                        success_count += 1
                    
                    total_rewards.append(episode_reward)
                    total_steps.append(steps)
                    
                    # 打印进度
                    print(f"测试 {i+1}/{num_tests_per_count}: {'成功' if success else '失败'}, 步数: {steps}, 奖励: {episode_reward:.2f}")
                
                # 计算统计数据
                success_rate = success_count / num_tests_per_count * 100
                avg_reward = np.mean(total_rewards)
                avg_steps = np.mean(total_steps)
                
                results[count] = {
                    'success_rate': success_rate,
                    'avg_reward': avg_reward,
                    'avg_steps': avg_steps
                }
                
                print(f"障碍物数量 {count}: 成功率 {success_rate:.2f}%, 平均奖励 {avg_reward:.2f}, 平均步数 {avg_steps:.2f}")
                
                env.close()
            
            # 绘制结果
            self._plot_stress_test_results(results, obstacle_counts)
            
            return results

def main():
    """主测试函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试TD3路径规划智能体')
    parser.add_argument('--model', type=str, help='模型路径')
    parser.add_argument('--obstacles', type=int, default=8, help='障碍物数量')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("TD3 路径规划模型测试")
    print("=" * 50)
    
    if args.model:
        tester = PathPlanningTester(args.model, num_obstacles=args.obstacles)
    else:
        # 查找可用的模型文件
        models_dir = "./models/training_20250531_160349"
        if not os.path.exists(models_dir):
            print(f"错误: 模型目录 {models_dir} 不存在!")
            return
        
        models = [f for f in os.listdir(models_dir) if f.endswith(".zip")]
        if not models:
            print(f"错误: 模型目录 {models_dir} 中没有找到模型文件!")
            return
        
        print("\n可用模型:")
        for i, model in enumerate(models):
            print(f"[{i}] {model}")
        
        choice = input("\n请选择模型编号: ").strip()
        if not choice.isdigit() or int(choice) >= len(models):
            print("无效选择!")
            return
        
        model_path = os.path.join(models_dir, models[int(choice)])
        obstacles = input("\n请输入障碍物数量 (默认8): ").strip()
        obstacles = int(obstacles) if obstacles.isdigit() else 8
        
        tester = PathPlanningTester(model_path, num_obstacles=obstacles)
    
    # 测试菜单
    while True:
        print("\n" + "=" * 50)
        print("测试选项:")
        print("[0] 退出")
        print("[1] 单回合测试 (带渲染)")
        print("[2] 单回合测试 (无渲染)")
        print("[3] 批量测试")
        print("[4] 压力测试")
        print("[5] 可视化轨迹")
        print("=" * 50)
        
        choice = input("请选择测试类型: ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            result = tester.single_episode_test(render=True)
            if result:
                tester.visualize_trajectory(result)
        elif choice == "2":
            result = tester.single_episode_test(render=False)
        elif choice == "3":
            num_tests = input("请输入测试次数 (默认30): ").strip()
            num_tests = int(num_tests) if num_tests.isdigit() else 30
            use_fixed = input("是否使用固定场景进行测试？(y/n，默认y): ").strip().lower()
            use_fixed = False if use_fixed == 'n' else True
            if use_fixed:
                print("将使用固定场景进行测试，确保评估结果的一致性")
            else:
                print("将使用随机场景进行测试")
            tester.batch_test(num_tests, use_fixed_scenarios=use_fixed)
        elif choice == "4":
            tester.stress_test()
        elif choice == "5":
            result = tester.single_episode_test(render=False)
            if result:
                tester.visualize_trajectory(result)
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()


def test_td3_agent(model_path, num_episodes=10, num_obstacles=8, render=True, save_video=False):
    """测试TD3智能体"""
    
    print("=" * 50)
    print("TD3 路径规划测试开始")
    print(f"模型路径: {model_path}")
    print(f"测试回合: {num_episodes}")
    print(f"障碍物数量: {num_obstacles}")
    print("=" * 50)
    
    # 创建环境
    env = PathPlanningEnv(
        map_size=100,
        num_obstacles=num_obstacles,
        robot_radius=2.5,
        render_mode='human' if render else None
    )
    
    # 加载模型
    model = TD3.load(model_path)
    
    # 视频保存设置
    if save_video:
        video_dir = "./videos"
        os.makedirs(video_dir, exist_ok=True)
        video_path = os.path.join(video_dir, f"td3_test_{os.path.basename(model_path)}.mp4")
        frames = []
    
    # 测试统计
    episode_rewards = []
    episode_lengths = []
    success_count = 0
    
    for episode in range(num_episodes):
        obs, _ = env.reset()
        done = False
        episode_reward = 0
        step_count = 0
        
        print(f"\n回合 {episode+1}/{num_episodes} 开始")
        print(f"起点: ({env.start_pos[0]:.2f}, {env.start_pos[1]:.2f})")
        print(f"终点: ({env.target_pos[0]:.2f}, {env.target_pos[1]:.2f})")
        
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            episode_reward += reward
            step_count += 1
            
            if render:
                env.render()
                
                if save_video:
                    # 保存当前帧
                    fig = plt.gcf()
                    fig.canvas.draw()
                    image = np.frombuffer(fig.canvas.tostring_rgb(), dtype='uint8')
                    image = image.reshape(fig.canvas.get_width_height()[::-1] + (3,))
                    frames.append(image)
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(step_count)
        
        if info.get('success', False):
            success_count += 1
            print(f"回合 {episode+1} 成功! 步数: {step_count}, 奖励: {episode_reward:.2f}")
        else:
            print(f"回合 {episode+1} 失败. 步数: {step_count}, 奖励: {episode_reward:.2f}")
    
    # 保存视频
    if save_video and frames:
        try:
            import cv2
            height, width, _ = frames[0].shape
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video = cv2.VideoWriter(video_path, fourcc, 10, (width, height))
            
            for frame in frames:
                video.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
            
            video.release()
            print(f"\n视频已保存到: {video_path}")
        except Exception as e:
            print(f"保存视频失败: {e}")
    
    # 打印测试结果
    print("\n测试结果汇总:")
    print(f"平均奖励: {np.mean(episode_rewards):.2f} ± {np.std(episode_rewards):.2f}")
    print(f"平均步数: {np.mean(episode_lengths):.2f} ± {np.std(episode_lengths):.2f}")
    print(f"成功率: {success_count/num_episodes*100:.2f}%")
    
    env.close()
    return episode_rewards, episode_lengths, success_count/num_episodes