# -*- coding: utf-8 -*-
"""
完整的集成路径规划系统
结合A*全局路径规划和强化学习局部路径规划
在当前栅格环境中进行局部路径规划
"""

import numpy as np
import pickle
import os
from typing import List, Tuple, Optional, Dict
import matplotlib.pyplot as plt
from collections import deque
import heapq
import gym
from stable_baselines3 import TD3
from ocean_data.ocean_data_extractor import OceanDataExtractor

# 设置中文字体
plt.rcParams['font.family'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class GridMap:
    """
    基于water_u数据的栅格地图
    """
    def __init__(self, water_u_data: np.ndarray, threshold: float = 2.0):
        """
        初始化栅格地图
        
        Args:
            water_u_data: water_u数据
            threshold: 阈值，超过此值的区域视为障碍物
        """
        # 确保数据是2D格式
        if len(water_u_data.shape) == 4:
            # 从(1, 1, 251, 250)提取为(251, 250)
            self.original_data = water_u_data[0, 0, :, :]
        elif len(water_u_data.shape) == 2:
            self.original_data = water_u_data
        else:
            raise ValueError(f"不支持的数据维度: {water_u_data.shape}")
        
        self.height, self.width = self.original_data.shape
        self.threshold = threshold
        
        # 数据预处理
        self.processed_data = self._preprocess_data()
        
        # 创建二值化障碍物地图
        self.obstacle_map = self._create_obstacle_map()
        
        # 统计信息
        total_cells = self.height * self.width
        obstacle_cells = np.sum(self.obstacle_map)
        free_cells = total_cells - obstacle_cells
        
        print(f"栅格地图初始化完成:")
        print(f"  地图尺寸: {self.height} x {self.width}")
        print(f"  数据范围: [{np.nanmin(self.original_data):.4f}, {np.nanmax(self.original_data):.4f}]")
        print(f"  NaN值数量: {np.sum(np.isnan(self.original_data))}")
        print(f"  总格子数: {total_cells}")
        print(f"  陆地格子(NaN): {obstacle_cells} ({obstacle_cells/total_cells*100:.1f}%)")
        print(f"  海洋格子: {free_cells} ({free_cells/total_cells*100:.1f}%)")
    
    def _preprocess_data(self) -> np.ndarray:
        """
        预处理数据
        
        Returns:
            np.ndarray: 预处理后的数据
        """
        # 保留原始数据，不处理NaN值，因为NaN代表陆地
        return self.original_data.copy()
    
    def _create_obstacle_map(self) -> np.ndarray:
        """
        根据water_u数据创建障碍物地图
        
        Returns:
            np.ndarray: 二值化障碍物地图，True表示障碍物
        """
        # 使用NaN值作为陆地障碍物
        obstacle_map = np.isnan(self.original_data)
        return obstacle_map
    
    def is_valid(self, x: int, y: int) -> bool:
        """
        检查坐标是否有效（在地图范围内且不是障碍物）
        
        Args:
            x: x坐标
            y: y坐标
            
        Returns:
            bool: 是否有效
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            return not self.obstacle_map[y, x]
        return False
    
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """
        获取有效的邻居节点（8连通）
        
        Args:
            x: x坐标
            y: y坐标
            
        Returns:
            List[Tuple[int, int]]: 有效邻居坐标列表
        """
        neighbors = []
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                     (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if self.is_valid(nx, ny):
                neighbors.append((nx, ny))
        
        return neighbors
    
    def find_valid_points(self, num_points: int = 10) -> List[Tuple[int, int]]:
        """
        寻找有效的点
        
        Args:
            num_points: 需要找到的点数
            
        Returns:
            List[Tuple[int, int]]: 有效点列表
        """
        valid_points = []
        
        # 在地图中均匀采样
        step_x = max(1, self.width // 20)
        step_y = max(1, self.height // 20)
        
        for y in range(step_y, self.height - step_y, step_y):
            for x in range(step_x, self.width - step_x, step_x):
                if self.is_valid(x, y):
                    valid_points.append((x, y))
                    if len(valid_points) >= num_points:
                        return valid_points
        
        return valid_points

class AStarPlanner:
    """
    A*路径规划算法
    """
    def __init__(self, grid_map: GridMap):
        """
        初始化A*规划器
        
        Args:
            grid_map: 栅格地图
        """
        self.grid_map = grid_map
    
    def heuristic(self, a: Tuple[int, int], b: Tuple[int, int]) -> float:
        """
        启发式函数（欧几里得距离）
        
        Args:
            a: 点A坐标
            b: 点B坐标
            
        Returns:
            float: 启发式距离
        """
        return np.sqrt((a[0] - b[0])**2 + (a[1] - b[1])**2)
    
    def plan(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """
        A*路径规划
        
        Args:
            start: 起始点坐标
            goal: 目标点坐标
            
        Returns:
            Optional[List[Tuple[int, int]]]: 路径点列表，如果无解则返回None
        """
        if not self.grid_map.is_valid(start[0], start[1]):
            print(f"起始点 {start} 无效")
            return None
        
        if not self.grid_map.is_valid(goal[0], goal[1]):
            print(f"目标点 {goal} 无效")
            return None
        
        # 初始化
        open_set = []
        heapq.heappush(open_set, (0, start))
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal)}
        visited = set()
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current in visited:
                continue
            visited.add(current)
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                path.reverse()
                return path
            
            for neighbor in self.grid_map.get_neighbors(current[0], current[1]):
                if neighbor in visited:
                    continue
                
                # 计算移动代价
                if abs(neighbor[0] - current[0]) + abs(neighbor[1] - current[1]) == 2:
                    # 对角线移动
                    tentative_g_score = g_score[current] + np.sqrt(2)
                else:
                    # 直线移动
                    tentative_g_score = g_score[current] + 1
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + self.heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        return None

class RLLocalPlanner:
    """
    基于强化学习的局部路径规划器
    在当前栅格环境中进行局部路径规划
    """
    def __init__(self, model_path: str, grid_map: GridMap):
        """
        初始化RL局部规划器
        
        Args:
            model_path: 训练好的模型路径
            grid_map: 栅格地图
        """
        self.grid_map = grid_map
        self.model_path = model_path
        self.model = None
        
        # 加载模型
        self._load_model()
    
    def _load_model(self):
        """
        加载训练好的RL模型
        """
        try:
            if os.path.exists(self.model_path):
                print(f"正在加载RL模型: {self.model_path}")
                self.model = TD3.load(self.model_path)
                print("RL模型加载成功")
            else:
                raise FileNotFoundError(f"RL模型文件不存在: {self.model_path}")
        except Exception as e:
            print(f"加载RL模型失败: {e}")
            raise e
    
    def plan_segment(self, start: Tuple[int, int], goal: Tuple[int, int], 
                    max_steps: int = 10, initial_angle: float = 0.0) -> Optional[Tuple[List[Tuple[float, float]], float]]:
        """
        在当前栅格环境中规划局部路径段
        
        Args:
            start: 起始点
            goal: 目标点
            max_steps: 最大步数
            initial_angle: 初始角度（上一次规划的终点角度）
            
        Returns:
            Optional[List[Tuple[float, float]]]: 路径段（浮点坐标），如果失败则返回None
        """
        if self.model is None:
            raise RuntimeError("RL模型未加载，无法进行路径规划")
        
        return self._rl_planning_in_grid(start, goal, max_steps, initial_angle)
    
    def _rl_planning_in_grid(self, start: Tuple[int, int], goal: Tuple[int, int], 
                            max_steps: int, initial_angle: float = 0.0) -> Optional[Tuple[List[Tuple[float, float]], float]]:
        """
        在当前栅格环境中使用RL模型进行路径规划
        
        Args:
            start: 起始点
            goal: 目标点
            max_steps: 最大步数
            initial_angle: 初始角度（上一次规划的终点角度）
            
        Returns:
            Optional[List[Tuple[float, float]]]: 路径段（浮点坐标）
        """
        try:
            # 使用浮点坐标存储路径，保持平滑性
            path = [(float(start[0]), float(start[1]))]
            current_pos = np.array([float(start[0]), float(start[1])], dtype=np.float32)
            target_pos = np.array([float(goal[0]), float(goal[1])], dtype=np.float32)
            
            # 机器人状态
            robot_vel = np.array([0.0, 0.0], dtype=np.float32)

            # 使用上一次局部规划的终点角度作为初始角度
            robot_angle = initial_angle

            dt = 0.2  # 时间步长
            
            for step in range(max_steps):
                # 构建观测向量（基于当前栅格环境）
                obs = self._get_grid_observation(current_pos, target_pos, robot_vel, robot_angle)
                
                # 使用RL模型预测动作
                action, _ = self.model.predict(obs, deterministic=True)
                
                # 执行动作并更新位置
                max_linear_vel = 2.0
                max_angular_vel = 2.0
                
                linear_vel = action[0] * max_linear_vel
                angular_vel = action[1] * max_angular_vel
                
                # 更新机器人状态
                robot_angle += angular_vel * dt
                robot_vel = np.array([
                    linear_vel * np.cos(robot_angle),
                    linear_vel * np.sin(robot_angle)
                ], dtype=np.float32)
                
                # 更新位置
                new_pos = current_pos + robot_vel * dt
                
                # 检查新位置是否有效（用于碰撞检测）
                new_grid_pos = (int(round(new_pos[0])), int(round(new_pos[1])))
                if self.grid_map.is_valid(new_grid_pos[0], new_grid_pos[1]):
                    current_pos = new_pos
                    # 保存浮点坐标而不是整数坐标，保持路径平滑性
                    path.append((float(new_pos[0]), float(new_pos[1])))
                    
                    # 检查是否到达目标
                    if np.linalg.norm(new_pos - target_pos) < 1.1:
                        break
                else:
                    # 如果新位置无效，停止规划
                    break
            
            # 返回路径和最终角度
            return (path, robot_angle) if len(path) > 1 else (None, initial_angle)
            
        except Exception as e:
            print(f"RL规划过程中出错: {e}")
            return None
    
    def _get_grid_observation(self, robot_pos: np.ndarray, target_pos: np.ndarray, 
                             robot_vel: np.ndarray, robot_angle: float) -> np.ndarray:
        """
        基于当前栅格环境构建观测向量，与训练环境保持一致
        
        Args:
            robot_pos: 机器人位置
            target_pos: 目标位置
            robot_vel: 机器人速度
            robot_angle: 机器人角度
            
        Returns:
            np.ndarray: 44维观测向量
        """
        # 1. 雷达数据（36维）
        radar_data = self._get_radar_readings(robot_pos, robot_angle)
        radar_normalized = radar_data / 5.0  # 雷达范围归一化
        
        # 2. 将目标位置转换到局部坐标系
        target_local = self._global_to_local(target_pos, robot_pos, robot_angle)
        
        # 3. 局部坐标系下的速度
        local_vel = self._get_local_velocity(robot_vel, robot_angle)
        
        # 4. 机器人状态（6维）- 与训练环境一致
        robot_state = np.array([
            0.0,  # 局部坐标系中机器人位置始终为原点
            0.0,
            target_local[0],  # 目标位置（局部坐标系）
            target_local[1],
            local_vel[0],  # 局部速度
            local_vel[1],
        ], dtype=np.float32)
        
        # 5. 相对目标信息（2维）
        distance = np.linalg.norm(target_local)
        angle_to_target = np.arctan2(target_local[1], target_local[0])
        relative_info = np.array([distance, angle_to_target], dtype=np.float32)
        
        # 组合观测向量：36 + 6 + 2 = 44维
        obs = np.concatenate([radar_normalized, robot_state, relative_info])
        
        return obs
    
    def _get_radar_readings(self, robot_pos: np.ndarray, robot_angle: float) -> np.ndarray:
        """
        获取36个方向的雷达读数
        
        Args:
            robot_pos: 机器人位置
            robot_angle: 机器人角度
            
        Returns:
            np.ndarray: 36维雷达读数
        """
        radar_range = 5.0
        radar_angles = np.linspace(-np.pi, np.pi, 36, endpoint=False)
        radar_readings = []
        
        for angle_offset in radar_angles:
            # 计算雷达射线的全局角度
            ray_angle = robot_angle + angle_offset
            
            # 沿射线方向搜索障碍物
            min_distance = radar_range
            
            # 射线步进
            step_size = 0.1
            for step in np.arange(step_size, radar_range + step_size, step_size):
                # 计算射线上的点
                ray_x = robot_pos[0] + step * np.cos(ray_angle)
                ray_y = robot_pos[1] + step * np.sin(ray_angle)
                
                # 检查是否超出地图边界
                if (ray_x < 0 or ray_x >= self.grid_map.width or 
                    ray_y < 0 or ray_y >= self.grid_map.height):
                    min_distance = step
                    break
                
                # 检查是否碰到障碍物
                grid_x, grid_y = int(ray_x), int(ray_y)
                if (0 <= grid_x < self.grid_map.width and 
                    0 <= grid_y < self.grid_map.height and 
                    self.grid_map.obstacle_map[grid_y, grid_x]):
                    min_distance = step
                    break
            
            radar_readings.append(min_distance)
        
        return np.array(radar_readings, dtype=np.float32)
    
    def _global_to_local(self, global_pos: np.ndarray, robot_pos: np.ndarray, robot_angle: float) -> np.ndarray:
        """
        将全局坐标转换为局部坐标
        
        Args:
            global_pos: 全局位置
            robot_pos: 机器人位置
            robot_angle: 机器人角度
            
        Returns:
            np.ndarray: 局部坐标
        """
        # 相对位置
        relative_pos = global_pos - robot_pos
        
        # 旋转到局部坐标系
        cos_angle = np.cos(-robot_angle)
        sin_angle = np.sin(-robot_angle)
        
        local_x = relative_pos[0] * cos_angle - relative_pos[1] * sin_angle
        local_y = relative_pos[0] * sin_angle + relative_pos[1] * cos_angle
        
        return np.array([local_x, local_y], dtype=np.float32)
    
    def _get_local_velocity(self, global_vel: np.ndarray, robot_angle: float) -> np.ndarray:
        """
        将全局速度转换为局部速度
        
        Args:
            global_vel: 全局速度
            robot_angle: 机器人角度
            
        Returns:
            np.ndarray: 局部速度
        """
        cos_angle = np.cos(-robot_angle)
        sin_angle = np.sin(-robot_angle)
        
        local_vx = global_vel[0] * cos_angle - global_vel[1] * sin_angle
        local_vy = global_vel[0] * sin_angle + global_vel[1] * cos_angle
        
        return np.array([local_vx, local_vy], dtype=np.float32)

class IntegratedPathPlanner:
    """
    集成路径规划器
    结合A*全局规划和RL局部规划
    """
    def __init__(self, grid_map: GridMap, model_path: str):
        """
        初始化集成规划器
        
        Args:
            grid_map: 栅格地图
            model_path: RL模型路径
        """
        self.grid_map = grid_map
        self.global_planner = AStarPlanner(grid_map)
        self.local_planner = RLLocalPlanner(model_path, grid_map)
        
        # 递减步长策略 
        self.step_sizes = [20, 15, 10, 8]
    
    def plan(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[Tuple[List[Tuple[float, float]], List[Tuple[int, int]]]]:
        """
        执行集成路径规划
        
        Args:
            start: 起始点
            goal: 目标点
            
        Returns:
            Optional[Tuple[List[Tuple[float, float]], List[Tuple[int, int]]]]: (优化路径, 全局路径)
        """
        print(f"开始集成路径规划: {start} -> {goal}")
        
        # 1. 全局路径规划
        print("1. 执行A*全局路径规划...")
        global_path = self.global_planner.plan(start, goal)
        
        if global_path is None:
            print("全局路径规划失败")
            return None
        
        print(f"全局路径规划成功，路径长度: {len(global_path)}")
        
        # 2. 局部路径优化
        print("2. 执行RL局部路径优化...")
        optimized_path = self._optimize_with_rl(global_path)
        
        if optimized_path:
            print(f"局部路径优化成功，优化后路径长度: {len(optimized_path)}")
            return optimized_path, global_path
        else:
            print("局部路径优化失败，返回全局路径")
            # 将全局路径转换为浮点坐标格式
            global_path_float = [(float(p[0]), float(p[1])) for p in global_path]
            return global_path_float, global_path
    
    def _optimize_with_rl(self, global_path: List[Tuple[int, int]]) -> Optional[List[Tuple[float, float]]]:
        """
        使用RL优化全局路径
        
        Args:
            global_path: 全局路径
            
        Returns:
            Optional[List[Tuple[float, float]]]: 优化后的路径（浮点坐标）
        """
        if len(global_path) < 2:
            # 将全局路径转换为浮点坐标格式
            return [(float(p[0]), float(p[1])) for p in global_path]
        
        # 将起始点转换为浮点坐标
        optimized_path = [(float(global_path[0][0]), float(global_path[0][1]))]
        current_idx = 0
        # 记录上一次局部规划的终点位置和角度
        last_end_pos = (float(global_path[0][0]), float(global_path[0][1]))
        last_angle = 0.0
        
        while current_idx < len(global_path) - 1:
            # 使用上一次局部规划的终点作为当前起点
            # TODO
            current_pos = (int(round(last_end_pos[0])), int(round(last_end_pos[1])))
            
            # 尝试不同的步长
            success = False
            
            for step_size in self.step_sizes:
                target_idx = min(current_idx + step_size, len(global_path) - 1)
                target_pos = global_path[target_idx]
                
                print(f"尝试从 {current_pos} 到 {target_pos} (步长={step_size})")
                
                # 使用RL规划局部路径，传入上一次的角度
                result = self.local_planner.plan_segment(
                    (int(round(last_end_pos[0])), int(round(last_end_pos[1]))), 
                    target_pos, step_size*15, last_angle
                )
                
                if result and result[0] and len(result[0]) > 1:
                    local_path, final_angle = result
                    # 局部规划成功
                    optimized_path.extend(local_path[1:])  # 排除起始点避免重复
                    # 更新上一次规划的终点位置和角度
                    last_end_pos = local_path[-1]
                    # 使用RL规划过程中智能体的最终角度
                    last_angle = final_angle
                    current_idx = target_idx
                    success = True
                    print(f"局部规划成功，步长={len(local_path)}")
                    break
                else:
                    print(f"局部规划失败，步长={step_size}")
            
            if not success:
                # 所有步长都失败，使用全局路径的下一个点
                current_idx += 1
                if current_idx < len(global_path):
                    # 将全局路径点转换为浮点坐标
                    point = global_path[current_idx]
                    optimized_path.append((float(point[0]), float(point[1])))
                print("使用全局路径的下一个点")
        
        return optimized_path
    
    def visualize_result(self, start: Tuple[int, int], goal: Tuple[int, int],
                        global_path: Optional[List[Tuple[int, int]]] = None,
                        optimized_path: Optional[List[Tuple[float, float]]] = None,
                        save_path: str = "complete_path_planning_result1.png"):
        """
        可视化规划结果
        
        Args:
            start: 起始点
            goal: 目标点
            global_path: 全局路径
            optimized_path: 优化后的路径
            save_path: 保存路径
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))
        
        # 显示栅格地图
        vis_map = np.zeros((self.grid_map.height, self.grid_map.width, 3))
        vis_map[self.grid_map.obstacle_map] = [0.2, 0.2, 0.2]  # 陆地为深灰色
        vis_map[~self.grid_map.obstacle_map] = [0.9, 0.9, 0.9]  # 海洋为浅灰色
        
        ax.imshow(vis_map, origin='lower')
        
        # 显示全局路径
        if global_path:
            path_x = [p[0] for p in global_path]
            path_y = [p[1] for p in global_path]
            ax.plot(path_x, path_y, 'b--', linewidth=2, label='全局路径(A*)', alpha=0.7)
        
        # 显示优化后的路径
        if optimized_path:
            opt_x = [p[0] for p in optimized_path]
            opt_y = [p[1] for p in optimized_path]
            ax.plot(opt_x, opt_y, 'r-', linewidth=3, label='优化路径(RL+A*)', alpha=0.9)
        
        # 显示起始点和目标点
        ax.plot(start[0], start[1], 'go', markersize=12, label='起始点', markeredgecolor='black', markeredgewidth=2)
        ax.plot(goal[0], goal[1], 'ro', markersize=12, label='目标点', markeredgecolor='black', markeredgewidth=2)
        
        ax.set_title('集成路径规划结果', fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X坐标', fontsize=12)
        ax.set_ylabel('Y坐标', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
        plt.close()

def load_water_u_data(data_path: str) -> np.ndarray:
    """
    从pickle文件中加载water_u数据
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        np.ndarray: water_u数据
    """
    try:
        print(f"正在加载数据: {data_path}")
        extractor = OceanDataExtractor(data_path)
        
        if not extractor.load_data():
            raise ValueError("无法加载海洋数据")
        
        # 先提取变量数据
        extracted_data = extractor.extract_variables()
        print(f"提取的数据类型: {list(extracted_data.keys())}")
        
        # 获取water_u数据
        water_u_data = extractor.get_variable_data('water_u')
        
        if water_u_data is None:
            raise ValueError("未找到water_u数据")
        
        # 处理字典格式的数据
        if isinstance(water_u_data, dict):
            if 'data' in water_u_data:
                water_u_data = water_u_data['data']
            else:
                raise ValueError("water_u数据格式不正确")
        
        print(f"成功加载water_u数据:")
        print(f"  数据形状: {water_u_data.shape}")
        print(f"  数据类型: {water_u_data.dtype}")
        print(f"  数据范围: [{np.nanmin(water_u_data):.4f}, {np.nanmax(water_u_data):.4f}]")
        
        return water_u_data
        
    except Exception as e:
        print(f"加载数据时出错: {e}")
        raise e

def calculate_path_metrics(path: List[Tuple[float, float]]) -> Dict[str, float]:
    """
    计算路径指标
    
    Args:
        path: 路径点列表（浮点坐标）
        
    Returns:
        Dict[str, float]: 路径指标
    """
    if len(path) < 2:
        return {"length": 0, "total_distance": 0}
    
    total_distance = 0
    for i in range(len(path) - 1):
        dx = path[i+1][0] - path[i][0]
        dy = path[i+1][1] - path[i][1]
        total_distance += np.sqrt(dx*dx + dy*dy)
    
    return {
        "length": len(path),
        "total_distance": total_distance
    }

def main():
    """
    主函数
    """
    print("=" * 60)
    print("完整的集成路径规划演示")
    print("=" * 60)
    
    # 1. 加载数据
    print("\n1. 加载ocean数据...")
    data_path = "ocean_data/extracted_ocean_data.pkl"
    
    try:
        water_u_data = load_water_u_data(data_path)
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 2. 创建栅格地图
    print("\n2. 创建栅格地图...")
    grid_map = GridMap(water_u_data)
    
    # 3. 设置起始点和目标点
    print("\n3. 设置起始点和目标点...")
    
    # 用户自定义起始点和目标点
    user_start = (200, 100)
    user_goal = (50, 225)
    
    # 验证用户定义的点是否有效
    valid_points = grid_map.find_valid_points(10)
    print(f"找到 {len(valid_points)} 个有效点")
    print(f"前10个有效点: {valid_points[:10]}")
    
    # 检查用户定义的点
    if grid_map.is_valid(user_start[0], user_start[1]):
        start = user_start
        print(f"使用用户定义的起始点: {start}")
    else:
        start = valid_points[0] if valid_points else (10, 10)
        print(f"用户定义的起始点无效，使用默认起始点: {start}")
    
    if grid_map.is_valid(user_goal[0], user_goal[1]):
        goal = user_goal
        print(f"使用用户定义的目标点: {goal}")
    else:
        goal = valid_points[-1] if valid_points else (240, 240)
        print(f"用户定义的目标点无效，使用默认目标点: {goal}")
    
    # 4. 创建集成路径规划器
    print("\n4. 创建集成路径规划器...")
    model_path = "models/training_20250531_160349/td3_pathplanning_2000000_steps.zip"
    
    try:
        planner = IntegratedPathPlanner(grid_map, model_path)
    except Exception as e:
        print(f"创建规划器失败: {e}")
        return
    
    # 5. 执行路径规划
    print("\n5. 执行集成路径规划...")
    result = planner.plan(start, goal)
    
    if result is None:
        print("路径规划失败")
        return
    
    optimized_path, global_path = result
    
    # 6. 计算路径指标
    print("\n6. 计算路径指标...")
    global_metrics = calculate_path_metrics(global_path)
    optimized_metrics = calculate_path_metrics(optimized_path)
    
    print(f"\n集成路径规划成功！")
    print(f"最终路径长度: {optimized_metrics['length']} 个点")
    print(f"全局路径长度: {global_metrics['length']} 个点")
    
    if global_metrics['length'] > 0:
        optimization_ratio = (global_metrics['length'] - optimized_metrics['length']) / global_metrics['length'] * 100
        print(f"路径优化程度: {optimization_ratio:.1f}%")
    
    print(f"路径总长度: {optimized_metrics['total_distance']:.2f}")
    
    # 计算路径效率（直线距离/实际路径长度）
    direct_distance = np.sqrt((goal[0] - start[0])**2 + (goal[1] - start[1])**2)
    if optimized_metrics['total_distance'] > 0:
        efficiency = direct_distance / optimized_metrics['total_distance'] * 100
        print(f"路径效率: {efficiency:.1f}%")
    
    # 7. 生成可视化结果
    print("\n7. 生成可视化结果...")
    planner.visualize_result(start, goal, global_path, optimized_path)
    
    print("\n完整的集成路径规划演示完成！")
    
    print("\n功能说明:")
    print("- 从ocean_data.pkl提取water_u数据并转换为2D栅格地图")
    print("- 使用A*算法进行全局路径规划")
    print("- 使用强化学习在当前栅格环境中进行局部路径优化")
    print("- 实现递减步长策略：10 -> 8 -> 5")
    print("- 生成完整的可视化结果")

if __name__ == "__main__":
    main()