import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def plot_training_results_improved(log_dir):
    """改进的训练结果绘制函数"""
    print(f"正在分析日志目录: {log_dir}")
    
    try:
        # 检查目录是否存在
        if not os.path.exists(log_dir):
            print(f"错误: 日志目录不存在: {log_dir}")
            return
        
        # 查找monitor文件
        monitor_files = [f for f in os.listdir(log_dir) if f.endswith('.monitor.csv')]
        print(f"找到 {len(monitor_files)} 个monitor文件: {monitor_files}")
        
        if not monitor_files:
            print("未找到训练日志文件(.monitor.csv)")
            return
        
        all_rewards = []
        all_lengths = []
        all_times = []
        successful_files = 0
        
        for file in monitor_files:
            file_path = os.path.join(log_dir, file)
            print(f"\n处理文件: {file}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"  文件不存在: {file_path}")
                continue
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            print(f"  文件大小: {file_size} 字节")
            
            if file_size == 0:
                print(f"  文件为空，跳过")
                continue
            
            try:
                # 尝试读取文件
                with open(file_path, 'r') as f:
                    lines = f.readlines()
                    print(f"  文件行数: {len(lines)}")
                    if len(lines) > 0:
                        print(f"  文件头: {lines[0].strip()}")
                    if len(lines) > 1:
                        print(f"  第二行: {lines[1].strip()}")
                
                # 使用numpy读取数据，跳过前两行（JSON元数据和列标题）
                data = np.loadtxt(file_path, delimiter=',', skiprows=2)
                print(f"  数据形状: {data.shape}")
                
                # 处理不同的数据形状
                if len(data.shape) == 1:  # 只有一行数据
                    if len(data) >= 2:
                        all_rewards.append(data[0])
                        all_lengths.append(data[1])
                        if len(data) >= 3:
                            all_times.append(data[2])
                        print(f"  添加单行数据: reward={data[0]}, length={data[1]}")
                elif len(data.shape) == 2:  # 多行数据
                    all_rewards.extend(data[:, 0])
                    all_lengths.extend(data[:, 1])
                    if data.shape[1] >= 3:
                        all_times.extend(data[:, 2])
                    print(f"  添加 {data.shape[0]} 行数据")
                
                successful_files += 1
                
            except Exception as e:
                print(f"  读取文件失败: {e}")
                # 尝试手动解析
                try:
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[2:]  # 跳过前两行（JSON元数据和列标题）
                        parsed_count = 0
                        for line in lines:
                            if line.strip():
                                parts = line.strip().split(',')
                                if len(parts) >= 2:
                                    try:
                                        all_rewards.append(float(parts[0]))
                                        all_lengths.append(float(parts[1]))
                                        if len(parts) >= 3:
                                            all_times.append(float(parts[2]))
                                        parsed_count += 1
                                    except ValueError:
                                        continue  # 跳过无法转换的行
                    print(f"  手动解析成功，添加 {parsed_count} 行数据")
                    successful_files += 1
                except Exception as e2:
                    print(f"  手动解析也失败: {e2}")
                    continue
        
        print(f"\n成功处理 {successful_files} 个文件")
        print(f"总共收集到 {len(all_rewards)} 个奖励数据点")
        print(f"总共收集到 {len(all_lengths)} 个长度数据点")
        
        if len(all_rewards) == 0:
            print("无法读取任何训练数据")
            return
        
        # 数据统计
        print(f"\n数据统计:")
        print(f"奖励范围: {min(all_rewards):.2f} ~ {max(all_rewards):.2f}")
        print(f"平均奖励: {np.mean(all_rewards):.2f}")
        print(f"长度范围: {min(all_lengths):.0f} ~ {max(all_lengths):.0f}")
        print(f"平均长度: {np.mean(all_lengths):.0f}")
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 奖励曲线
        episodes = range(len(all_rewards))
        ax1.plot(episodes, all_rewards, alpha=0.6, color='lightblue', label='原始奖励', linewidth=0.5)
        
        # 计算移动平均
        window_size = max(1, min(100, len(all_rewards) // 10))
        if window_size > 1 and len(all_rewards) >= window_size:
            smoothed_rewards = np.convolve(all_rewards, np.ones(window_size)/window_size, mode='valid')
            smooth_episodes = range(window_size-1, len(all_rewards))
            ax1.plot(smooth_episodes, smoothed_rewards, label=f'平滑奖励 (窗口={window_size})', color='blue', linewidth=2)
        
        ax1.set_xlabel('Episode')
        ax1.set_ylabel('总奖励')
        ax1.set_title('训练奖励曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 回合长度曲线
        ax2.plot(episodes, all_lengths, alpha=0.6, color='lightcoral', label='原始长度', linewidth=0.5)
        
        if window_size > 1 and len(all_lengths) >= window_size:
            smoothed_lengths = np.convolve(all_lengths, np.ones(window_size)/window_size, mode='valid')
            ax2.plot(smooth_episodes, smoothed_lengths, label=f'平滑长度 (窗口={window_size})', color='red', linewidth=2)
        
        ax2.set_xlabel('Episode')
        ax2.set_ylabel('回合长度')
        ax2.set_title('训练回合长度曲线')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(log_dir, 'training_curves_improved.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"\n图表已保存到: {output_path}")
        
        plt.show()
        
        # 保存数据摘要
        summary_path = os.path.join(log_dir, 'training_summary.txt')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"训练数据摘要\n")
            f.write(f"="*50 + "\n")
            f.write(f"处理的文件数: {successful_files}\n")
            f.write(f"总episode数: {len(all_rewards)}\n")
            f.write(f"奖励统计:\n")
            f.write(f"  最小值: {min(all_rewards):.2f}\n")
            f.write(f"  最大值: {max(all_rewards):.2f}\n")
            f.write(f"  平均值: {np.mean(all_rewards):.2f}\n")
            f.write(f"  标准差: {np.std(all_rewards):.2f}\n")
            f.write(f"长度统计:\n")
            f.write(f"  最小值: {min(all_lengths):.0f}\n")
            f.write(f"  最大值: {max(all_lengths):.0f}\n")
            f.write(f"  平均值: {np.mean(all_lengths):.0f}\n")
            f.write(f"  标准差: {np.std(all_lengths):.0f}\n")
        
        print(f"数据摘要已保存到: {summary_path}")
        
    except Exception as e:
        print(f"绘制过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试函数
    import sys
    
    if len(sys.argv) > 1:
        log_dir = sys.argv[1]
    else:
        # 默认测试路径
        base_dir = r"f:\24Graduate_Student\Path_Planning_Algorithm\DRL\TD3-test\My_TD3\My-TD3_V1\My-TD3_V2\logs"
        
        # 列出所有可用的日志目录
        if os.path.exists(base_dir):
            log_dirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
            print("可用的日志目录:")
            for i, dir_name in enumerate(log_dirs):
                print(f"  {i+1}. {dir_name}")
            
            if log_dirs:
                # 使用最新的日志目录
                latest_dir = max(log_dirs)
                log_dir = os.path.join(base_dir, latest_dir)
                print(f"\n使用最新的日志目录: {latest_dir}")
            else:
                print("未找到任何日志目录")
                sys.exit(1)
        else:
            print(f"日志基础目录不存在: {base_dir}")
            sys.exit(1)
    
    plot_training_results_improved(log_dir)