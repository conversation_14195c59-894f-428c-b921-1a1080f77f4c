#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果的简单脚本
"""

import os
import sys
import numpy as np

def verify_data_reading(log_dir):
    """验证数据读取功能"""
    print(f"验证日志目录: {log_dir}")
    print("=" * 50)
    
    # 检查目录是否存在
    if not os.path.exists(log_dir):
        print(f"错误: 日志目录不存在: {log_dir}")
        return False
    
    # 查找monitor文件
    monitor_files = [f for f in os.listdir(log_dir) if f.endswith('.monitor.csv')]
    print(f"找到 {len(monitor_files)} 个monitor文件: {monitor_files}")
    
    if not monitor_files:
        print("未找到训练日志文件(.monitor.csv)")
        return False
    
    all_rewards = []
    all_lengths = []
    successful_files = 0
    
    for file in monitor_files:
        file_path = os.path.join(log_dir, file)
        print(f"\n处理文件: {file}")
        
        # 检查文件是否存在和大小
        if not os.path.exists(file_path):
            print(f"  ❌ 文件不存在: {file_path}")
            continue
        
        file_size = os.path.getsize(file_path)
        print(f"  📁 文件大小: {file_size} 字节")
        
        if file_size == 0:
            print(f"  ⚠️  文件为空，跳过")
            continue
        
        # 查看文件内容
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()
                print(f"  📄 文件行数: {len(lines)}")
                if len(lines) > 0:
                    print(f"  📋 文件头: {lines[0].strip()}")
                if len(lines) > 1:
                    print(f"  📋 第二行: {lines[1].strip()}")
        except Exception as e:
            print(f"  ❌ 无法读取文件内容: {e}")
            continue
        
        # 尝试numpy读取，跳过前两行（JSON元数据和列标题）
        try:
            data = np.loadtxt(file_path, delimiter=',', skiprows=2)
            print(f"  ✅ numpy读取成功，数据形状: {data.shape}")
            
            # 处理不同的数据形状
            if len(data.shape) == 1:  # 只有一行数据
                if len(data) >= 2:
                    all_rewards.append(data[0])
                    all_lengths.append(data[1])
                    print(f"  📊 添加单行数据: reward={data[0]:.2f}, length={data[1]:.0f}")
            elif len(data.shape) == 2:  # 多行数据
                all_rewards.extend(data[:, 0])
                all_lengths.extend(data[:, 1])
                print(f"  📊 添加 {data.shape[0]} 行数据")
                print(f"  📊 奖励范围: {data[:, 0].min():.2f} ~ {data[:, 0].max():.2f}")
                print(f"  📊 长度范围: {data[:, 1].min():.0f} ~ {data[:, 1].max():.0f}")
            
            successful_files += 1
            
        except Exception as e:
            print(f"  ⚠️  numpy读取失败: {e}")
            print(f"  🔧 尝试手动解析...")
            
            # 尝试手动解析
            try:
                with open(file_path, 'r') as f:
                    lines = f.readlines()[2:]  # 跳过前两行（JSON元数据和列标题）
                    parsed_count = 0
                    for line in lines:
                        if line.strip():
                            parts = line.strip().split(',')
                            if len(parts) >= 2:
                                try:
                                    all_rewards.append(float(parts[0]))
                                    all_lengths.append(float(parts[1]))
                                    if len(parts) >= 3:
                                        all_times.append(float(parts[2]))
                                    parsed_count += 1
                                except ValueError:
                                    continue  # 跳过无法转换的行
            except Exception as e2:
                print(f"  ❌ 手动解析也失败: {e2}")
                continue
    
    print(f"\n" + "=" * 50)
    print(f"📈 处理结果摘要:")
    print(f"  成功处理文件数: {successful_files}/{len(monitor_files)}")
    print(f"  收集到的数据点: {len(all_rewards)}")
    
    if len(all_rewards) > 0:
        print(f"  奖励统计:")
        print(f"    最小值: {min(all_rewards):.2f}")
        print(f"    最大值: {max(all_rewards):.2f}")
        print(f"    平均值: {np.mean(all_rewards):.2f}")
        print(f"    标准差: {np.std(all_rewards):.2f}")
        
        print(f"  长度统计:")
        print(f"    最小值: {min(all_lengths):.0f}")
        print(f"    最大值: {max(all_lengths):.0f}")
        print(f"    平均值: {np.mean(all_lengths):.0f}")
        print(f"    标准差: {np.std(all_lengths):.0f}")
        
        print(f"\n✅ 数据读取验证成功！")
        return True
    else:
        print(f"\n❌ 无法读取任何训练数据")
        return False

def main():
    """主函数"""
    print("训练数据读取验证工具")
    print("=" * 50)
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    logs_dir = os.path.join(project_root, "logs")
    
    print(f"项目根目录: {project_root}")
    print(f"日志目录: {logs_dir}")
    
    if not os.path.exists(logs_dir):
        print(f"错误: 日志目录不存在: {logs_dir}")
        return
    
    # 获取所有日志子目录
    log_subdirs = []
    for item in os.listdir(logs_dir):
        item_path = os.path.join(logs_dir, item)
        if os.path.isdir(item_path):
            log_subdirs.append(item)
    
    if not log_subdirs:
        print("未找到任何日志子目录")
        return
    
    print(f"\n找到 {len(log_subdirs)} 个日志目录:")
    for i, subdir in enumerate(log_subdirs, 1):
        subdir_path = os.path.join(logs_dir, subdir)
        monitor_files = [f for f in os.listdir(subdir_path) if f.endswith('.monitor.csv')]
        print(f"  {i}. {subdir} (包含 {len(monitor_files)} 个monitor文件)")
    
    # 测试最新的目录
    latest_dir = max(log_subdirs)
    latest_path = os.path.join(logs_dir, latest_dir)
    
    print(f"\n🔍 测试最新目录: {latest_dir}")
    success = verify_data_reading(latest_path)
    
    if success:
        print(f"\n🎉 验证完成！数据读取功能正常工作。")
        print(f"现在可以使用改进的绘制功能来生成训练曲线图。")
    else:
        print(f"\n⚠️  验证失败，请检查数据文件。")

if __name__ == "__main__":
    main()