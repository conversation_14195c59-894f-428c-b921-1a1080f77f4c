# 训练结果绘制测试工具

这个文件夹包含了改进的训练结果绘制功能和相关的测试工具。

## 文件说明

### `improved_plot_training.py`
改进的训练结果绘制函数，解决了原始代码中的数据读取问题：

**主要改进：**
- 详细的错误诊断和调试信息
- 处理单行数据和多行数据的情况
- 手动解析CSV文件作为备选方案
- 文件存在性和大小检查
- 数据统计和摘要生成
- 更好的异常处理

**功能特性：**
- 自动检测和处理不同格式的monitor文件
- 生成详细的处理日志
- 保存高质量的训练曲线图片
- 生成训练数据摘要文件
- 支持中文显示

### `test_plot.py`
测试脚本，提供两种测试模式：

1. **单个目录测试模式**：选择特定的日志目录进行测试
2. **批量测试模式**：测试所有可用的日志目录

## 使用方法

### 方法1：直接运行测试脚本
```bash
cd test_plotting
python test_plot.py
```

### 方法2：直接使用改进的绘制函数
```bash
cd test_plotting
python improved_plot_training.py [日志目录路径]
```

如果不提供路径参数，程序会自动选择最新的日志目录。

### 方法3：在Python代码中使用
```python
from improved_plot_training import plot_training_results_improved

# 指定日志目录
log_dir = "path/to/your/log/directory"
plot_training_results_improved(log_dir)
```

## 输出文件

运行后会在指定的日志目录中生成以下文件：

1. `training_curves_improved.png` - 改进的训练曲线图
2. `training_summary.txt` - 训练数据统计摘要

## 问题诊断

如果遇到数据读取问题，改进的函数会提供详细的诊断信息：

- 文件存在性检查
- 文件大小检查
- 文件内容预览
- 数据格式分析
- 具体的错误信息

## 支持的数据格式

- 标准的monitor.csv格式（r,l,t）
- 单行数据文件
- 多行数据文件
- 包含或不包含时间戳的文件

## 注意事项

1. 确保安装了必要的依赖包：
   ```bash
   pip install numpy matplotlib
   ```

2. 如果遇到中文显示问题，请确保系统安装了SimHei或Microsoft YaHei字体

3. 对于大型数据集，绘制可能需要一些时间，请耐心等待

## 故障排除

### 常见问题：

1. **"无法读取训练数据"**
   - 检查monitor文件是否存在
   - 检查文件是否为空
   - 查看详细的诊断信息

2. **图表显示异常**
   - 检查matplotlib是否正确安装
   - 尝试更新matplotlib版本

3. **中文显示为方块**
   - 安装中文字体
   - 或修改代码中的字体设置

## 与原始代码的对比

| 特性 | 原始代码 | 改进代码 |
|------|----------|----------|
| 错误处理 | 简单的try-except | 详细的错误诊断 |
| 数据格式支持 | 仅多行数据 | 单行和多行数据 |
| 调试信息 | 无 | 详细的处理日志 |
| 文件检查 | 无 | 存在性和大小检查 |
| 备选方案 | 无 | 手动CSV解析 |
| 数据统计 | 无 | 完整的统计摘要 |