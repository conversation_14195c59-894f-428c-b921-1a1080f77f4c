# -*- coding: utf-8 -*-
"""
综合路径规划系统
结合A*全局路径规划和强化学习局部路径规划
使用ocean_data中的water_u数据作为栅格地图
"""

import numpy as np
import pickle
import os
from typing import List, Tuple, Optional, Dict
import matplotlib.pyplot as plt
from collections import deque
import heapq
from stable_baselines3 import TD3
import torch
from ocean_data.ocean_data_extractor import OceanDataExtractor
from environment import PathPlanningEnv

# 设置中文字体
plt.rcParams['font.family'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class GridMap:
    """
    栅格地图类，基于water_u数据构建
    """
    def __init__(self, water_u_data: np.ndarray, threshold: float = 0.5):
        """
        初始化栅格地图
        
        Args:
            water_u_data: water_u数据，形状为(251, 250)
            threshold: 阈值，超过此值的区域视为障碍物
        """
        # 确保数据是2D格式
        if len(water_u_data.shape) == 4:
            # 从(1, 1, 251, 250)提取为(251, 250)
            self.grid = water_u_data[0, 0, :, :]
        elif len(water_u_data.shape) == 2:
            self.grid = water_u_data
        else:
            raise ValueError(f"不支持的数据维度: {water_u_data.shape}")
        
        self.height, self.width = self.grid.shape
        self.threshold = threshold
        
        # 创建二值化障碍物地图
        self.obstacle_map = self._create_obstacle_map()
        
        print(f"栅格地图初始化完成: {self.height} x {self.width}")
        print(f"障碍物阈值: {threshold}")
        print(f"障碍物数量: {np.sum(self.obstacle_map)}")
    
    def _create_obstacle_map(self) -> np.ndarray:
        """
        根据water_u数据创建障碍物地图
        
        Returns:
            np.ndarray: 二值化障碍物地图，True表示障碍物
        """
        # 处理NaN值
        grid_clean = np.nan_to_num(self.grid, nan=0.0)
        
        # 使用绝对值判断是否为障碍物
        obstacle_map = np.abs(grid_clean) > self.threshold
        
        return obstacle_map
    
    def is_valid(self, x: int, y: int) -> bool:
        """
        检查坐标是否有效（在地图范围内且不是障碍物）
        
        Args:
            x: x坐标
            y: y坐标
            
        Returns:
            bool: 是否有效
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            return not self.obstacle_map[y, x]
        return False
    
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """
        获取有效的邻居节点（8连通）
        
        Args:
            x: x坐标
            y: y坐标
            
        Returns:
            List[Tuple[int, int]]: 有效邻居坐标列表
        """
        neighbors = []
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                     (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if self.is_valid(nx, ny):
                neighbors.append((nx, ny))
        
        return neighbors
    
    def visualize(self, path: Optional[List[Tuple[int, int]]] = None, 
                  start: Optional[Tuple[int, int]] = None,
                  goal: Optional[Tuple[int, int]] = None,
                  save_path: Optional[str] = None):
        """
        可视化栅格地图
        
        Args:
            path: 路径点列表
            start: 起始点
            goal: 目标点
            save_path: 保存路径
        """
        plt.figure(figsize=(12, 10))
        
        # 显示原始water_u数据
        plt.subplot(2, 2, 1)
        plt.imshow(self.grid, cmap='viridis', origin='lower')
        plt.colorbar(label='Water U')
        plt.title('原始Water_U数据')
        
        # 显示障碍物地图
        plt.subplot(2, 2, 2)
        plt.imshow(self.obstacle_map, cmap='gray', origin='lower')
        plt.title('障碍物地图')
        
        # 显示路径规划结果
        plt.subplot(2, 2, (3, 4))
        # 创建可视化地图
        vis_map = np.zeros((self.height, self.width, 3))
        vis_map[self.obstacle_map] = [0, 0, 0]  # 障碍物为黑色
        vis_map[~self.obstacle_map] = [1, 1, 1]  # 自由空间为白色
        
        plt.imshow(vis_map, origin='lower')
        
        # 绘制路径
        if path:
            path_x = [p[0] for p in path]
            path_y = [p[1] for p in path]
            plt.plot(path_x, path_y, 'b-', linewidth=2, label='路径')
        
        # 绘制起始点和目标点
        if start:
            plt.plot(start[0], start[1], 'go', markersize=10, label='起始点')
        if goal:
            plt.plot(goal[0], goal[1], 'ro', markersize=10, label='目标点')
        
        plt.title('路径规划结果')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"可视化结果已保存到: {save_path}")
        else:
            plt.show()

class AStarPlanner:
    """
    A*路径规划算法
    """
    def __init__(self, grid_map: GridMap):
        """
        初始化A*规划器
        
        Args:
            grid_map: 栅格地图
        """
        self.grid_map = grid_map
    
    def heuristic(self, a: Tuple[int, int], b: Tuple[int, int]) -> float:
        """
        启发式函数（欧几里得距离）
        
        Args:
            a: 点A坐标
            b: 点B坐标
            
        Returns:
            float: 启发式距离
        """
        return np.sqrt((a[0] - b[0])**2 + (a[1] - b[1])**2)
    
    def plan(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """
        A*路径规划
        
        Args:
            start: 起始点坐标
            goal: 目标点坐标
            
        Returns:
            Optional[List[Tuple[int, int]]]: 路径点列表，如果无解则返回None
        """
        if not self.grid_map.is_valid(start[0], start[1]):
            print(f"起始点 {start} 无效")
            return None
        
        if not self.grid_map.is_valid(goal[0], goal[1]):
            print(f"目标点 {goal} 无效")
            return None
        
        # 初始化
        open_set = []
        heapq.heappush(open_set, (0, start))
        came_from = {}
        g_score = {start: 0}
        f_score = {start: self.heuristic(start, goal)}
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                path.reverse()
                return path
            
            for neighbor in self.grid_map.get_neighbors(current[0], current[1]):
                # 计算移动代价
                if abs(neighbor[0] - current[0]) + abs(neighbor[1] - current[1]) == 2:
                    # 对角线移动
                    tentative_g_score = g_score[current] + np.sqrt(2)
                else:
                    # 直线移动
                    tentative_g_score = g_score[current] + 1
                
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = g_score[neighbor] + self.heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        print("A*算法未找到路径")
        return None

class RLLocalPlanner:
    """
    强化学习局部路径规划器
    """
    def __init__(self, model_path: str, env_config: Dict):
        """
        初始化RL局部规划器
        
        Args:
            model_path: 训练好的模型路径
            env_config: 环境配置
        """
        self.model_path = model_path
        self.env_config = env_config
        self.model = None
        self.env = None
        
        # 加载模型
        self._load_model()
    
    def _load_model(self):
        """
        加载训练好的强化学习模型
        """
        try:
            self.model = TD3.load(self.model_path)
            print(f"成功加载RL模型: {self.model_path}")
            
            # 创建环境用于局部规划
            self.env = PathPlanningEnv(**self.env_config)
            print("RL环境初始化完成")
            
        except Exception as e:
            print(f"加载RL模型失败: {e}")
            raise
    
    def plan_local_path(self, current_pos: Tuple[float, float], 
                       target_pos: Tuple[float, float],
                       max_steps: int = 10) -> List[Tuple[float, float]]:
        """
        使用强化学习进行局部路径规划
        
        Args:
            current_pos: 当前位置
            target_pos: 目标位置
            max_steps: 最大步数
            
        Returns:
            List[Tuple[float, float]]: 局部路径点列表
        """
        if self.model is None or self.env is None:
            print("RL模型或环境未初始化")
            return [current_pos, target_pos]
        
        try:
            # 设置环境状态
            self.env.robot_pos = np.array(current_pos)
            self.env.target_pos = np.array(target_pos)
            
            # 重置环境
            obs, _ = self.env.reset()
            
            path = [current_pos]
            
            for step in range(max_steps):
                # 使用模型预测动作
                action, _ = self.model.predict(obs, deterministic=True)
                
                # 执行动作
                obs, reward, terminated, truncated, info = self.env.step(action)
                
                # 记录当前位置
                current_robot_pos = tuple(self.env.robot_pos)
                path.append(current_robot_pos)
                
                # 检查是否到达目标或发生碰撞
                if terminated or truncated:
                    break
            
            return path
            
        except Exception as e:
            print(f"RL局部规划失败: {e}")
            return [current_pos, target_pos]

class IntegratedPathPlanner:
    """
    综合路径规划器
    结合A*全局规划和RL局部规划
    """
    def __init__(self, water_u_data: np.ndarray, model_path: str, 
                 threshold: float = 0.5, env_config: Optional[Dict] = None):
        """
        初始化综合路径规划器
        
        Args:
            water_u_data: water_u数据
            model_path: RL模型路径
            threshold: 障碍物阈值
            env_config: 环境配置
        """
        # 创建栅格地图
        self.grid_map = GridMap(water_u_data, threshold)
        
        # 创建A*规划器
        self.global_planner = AStarPlanner(self.grid_map)
        
        # 创建RL局部规划器
        if env_config is None:
            env_config = {
                'map_size': 100,
                'num_obstacles': 24,
                'robot_radius': 0.5,
                'use_ocean_data': True,
                'ocean_data_path': 'ocean_data/extracted_ocean_data.pkl'
            }
        
        self.local_planner = RLLocalPlanner(model_path, env_config)
        
        # 规划参数
        self.local_planning_lengths = [10, 8, 5]  # 递减的规划长度
        self.current_length_index = 0
    
    def plan_path(self, start: Tuple[int, int], goal: Tuple[int, int]) -> Dict:
        """
        综合路径规划
        
        Args:
            start: 起始点坐标（栅格坐标）
            goal: 目标点坐标（栅格坐标）
            
        Returns:
            Dict: 包含全局路径、局部路径和规划结果的字典
        """
        result = {
            'global_path': None,
            'local_paths': [],
            'final_path': [],
            'success': False,
            'planning_info': []
        }
        
        # 1. 全局路径规划
        print("开始A*全局路径规划...")
        global_path = self.global_planner.plan(start, goal)
        
        if global_path is None:
            print("全局路径规划失败")
            return result
        
        result['global_path'] = global_path
        print(f"全局路径规划成功，路径长度: {len(global_path)}")
        
        # 2. 沿全局路径进行局部规划
        print("开始RL局部路径规划...")
        current_pos = start
        final_path = [current_pos]
        
        i = 1
        while i < len(global_path):
            # 重置规划长度索引
            self.current_length_index = 0
            
            # 尝试不同的规划长度
            local_success = False
            
            for length_idx, max_length in enumerate(self.local_planning_lengths):
                # 确定局部目标点
                target_idx = min(i + max_length, len(global_path) - 1)
                target_pos = global_path[target_idx]
                
                print(f"尝试局部规划: 从 {current_pos} 到 {target_pos}，最大长度: {max_length}")
                
                # 执行局部规划
                local_path = self.local_planner.plan_local_path(
                    current_pos, target_pos, max_length
                )
                
                # 检查局部规划是否成功
                if self._validate_local_path(local_path, target_pos):
                    print(f"局部规划成功，长度: {max_length}")
                    result['local_paths'].append(local_path)
                    result['planning_info'].append({
                        'from': current_pos,
                        'to': target_pos,
                        'length': max_length,
                        'success': True
                    })
                    
                    # 添加到最终路径
                    final_path.extend(local_path[1:])  # 跳过起始点避免重复
                    current_pos = local_path[-1]
                    i = target_idx + 1
                    local_success = True
                    break
                else:
                    print(f"局部规划失败，长度: {max_length}")
                    result['planning_info'].append({
                        'from': current_pos,
                        'to': target_pos,
                        'length': max_length,
                        'success': False
                    })
            
            if not local_success:
                print("所有局部规划长度都失败，使用全局路径")
                # 如果所有长度都失败，直接使用全局路径的下一个点
                i += 1
                if i < len(global_path):
                    current_pos = global_path[i]
                    final_path.append(current_pos)
        
        result['final_path'] = final_path
        result['success'] = True
        
        print(f"综合路径规划完成，最终路径长度: {len(final_path)}")
        return result
    
    def _validate_local_path(self, local_path: List[Tuple[float, float]], 
                           target: Tuple[int, int], tolerance: float = 2.0) -> bool:
        """
        验证局部路径是否有效
        
        Args:
            local_path: 局部路径
            target: 目标点
            tolerance: 容忍距离
            
        Returns:
            bool: 是否有效
        """
        if not local_path or len(local_path) < 2:
            return False
        
        # 检查是否接近目标
        final_pos = local_path[-1]
        distance = np.sqrt((final_pos[0] - target[0])**2 + (final_pos[1] - target[1])**2)
        
        return distance <= tolerance
    
    def visualize_result(self, result: Dict, save_path: Optional[str] = None):
        """
        可视化规划结果
        
        Args:
            result: 规划结果
            save_path: 保存路径
        """
        if not result['success']:
            print("规划失败，无法可视化")
            return
        
        plt.figure(figsize=(15, 10))
        
        # 创建可视化地图
        vis_map = np.zeros((self.grid_map.height, self.grid_map.width, 3))
        vis_map[self.grid_map.obstacle_map] = [0, 0, 0]  # 障碍物为黑色
        vis_map[~self.grid_map.obstacle_map] = [1, 1, 1]  # 自由空间为白色
        
        plt.imshow(vis_map, origin='lower')
        
        # 绘制全局路径
        if result['global_path']:
            global_x = [p[0] for p in result['global_path']]
            global_y = [p[1] for p in result['global_path']]
            plt.plot(global_x, global_y, 'b--', linewidth=2, alpha=0.7, label='A*全局路径')
        
        # 绘制局部路径
        for i, local_path in enumerate(result['local_paths']):
            local_x = [p[0] for p in local_path]
            local_y = [p[1] for p in local_path]
            plt.plot(local_x, local_y, 'g-', linewidth=1.5, alpha=0.8)
        
        # 绘制最终路径
        if result['final_path']:
            final_x = [p[0] for p in result['final_path']]
            final_y = [p[1] for p in result['final_path']]
            plt.plot(final_x, final_y, 'r-', linewidth=3, label='最终路径')
        
        # 绘制起始点和目标点
        if result['global_path']:
            start = result['global_path'][0]
            goal = result['global_path'][-1]
            plt.plot(start[0], start[1], 'go', markersize=12, label='起始点')
            plt.plot(goal[0], goal[1], 'ro', markersize=12, label='目标点')
        
        plt.title('综合路径规划结果\n(蓝色虚线: A*全局路径, 绿色线: RL局部路径, 红色线: 最终路径)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"规划结果已保存到: {save_path}")
        else:
            plt.show()

def load_water_u_data(data_path: str) -> np.ndarray:
    """
    从pickle文件中加载water_u数据
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        np.ndarray: water_u数据
    """
    try:
        extractor = OceanDataExtractor(data_path)
        if not extractor.load_data():
            raise ValueError("无法加载海洋数据")
        
        extracted_data = extractor.extract_variables()
        water_u_data = extractor.get_variable_data('water_u')
        
        if water_u_data is None:
            raise ValueError("未找到water_u数据")
        
        # 处理字典格式的数据
        if isinstance(water_u_data, dict):
            if 'data' in water_u_data:
                water_u_data = water_u_data['data']
            else:
                raise ValueError("water_u数据格式不正确")
        
        print(f"成功加载water_u数据，形状: {water_u_data.shape}")
        return water_u_data
        
    except Exception as e:
        print(f"加载water_u数据失败: {e}")
        raise

def main():
    """
    主函数 - 演示综合路径规划系统
    """
    print("综合路径规划系统")
    print("=" * 50)
    
    # 配置参数
    data_path = "ocean_data/extracted_ocean_data.pkl"
    model_path = "models/best_model.zip"  # 使用最佳模型
    threshold = 1.0  # 障碍物阈值（调整为更合理的值）
    
    try:
        # 1. 加载water_u数据
        print("1. 加载water_u数据...")
        water_u_data = load_water_u_data(data_path)
        
        # 2. 创建综合路径规划器
        print("2. 初始化综合路径规划器...")
        planner = IntegratedPathPlanner(
            water_u_data=water_u_data,
            model_path=model_path,
            threshold=threshold
        )
        
        # 3. 设置起始点和目标点（调整到地图中心区域）
        height, width = planner.grid_map.height, planner.grid_map.width
        print(f"地图尺寸: {height} x {width}")
        
        # 寻找有效的起始点和目标点
        start = None
        goal = None
        
        # 在地图中心区域寻找有效点
        for y in range(height//4, 3*height//4, 10):
            for x in range(width//4, 3*width//4, 10):
                if planner.grid_map.is_valid(x, y):
                    if start is None:
                        start = (x, y)
                    elif goal is None and abs(x - start[0]) > 50 and abs(y - start[1]) > 50:
                        goal = (x, y)
                        break
            if start is not None and goal is not None:
                break
        
        if start is None or goal is None:
            print("无法找到有效的起始点和目标点，使用默认值")
            start = (width//4, height//4)
            goal = (3*width//4, 3*height//4)
        
        print(f"3. 开始路径规划: 从 {start} 到 {goal}")
        
        # 4. 执行路径规划
        result = planner.plan_path(start, goal)
        
        # 5. 显示规划结果
        if result['success']:
            print("\n路径规划成功！")
            print(f"全局路径长度: {len(result['global_path']) if result['global_path'] else 0}")
            print(f"局部路径段数: {len(result['local_paths'])}")
            print(f"最终路径长度: {len(result['final_path'])}")
            
            # 显示规划信息
            print("\n局部规划详情:")
            for i, info in enumerate(result['planning_info']):
                status = "成功" if info['success'] else "失败"
                print(f"  段{i+1}: {info['from']} -> {info['to']}, 长度{info['length']}, {status}")
        else:
            print("路径规划失败！")
        
        # 6. 可视化结果
        print("\n6. 生成可视化结果...")
        planner.visualize_result(result, "integrated_path_planning_result.png")
        
        # 7. 可视化栅格地图
        planner.grid_map.visualize(
            path=result['final_path'] if result['success'] else None,
            start=start,
            goal=goal,
            save_path="grid_map_visualization.png"
        )
        
        print("\n综合路径规划演示完成！")
        
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()