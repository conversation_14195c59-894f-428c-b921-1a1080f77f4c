# 海洋要素数据加载测试模块

本模块用于测试和处理海风、海浪、洋流等海洋要素的.nc格式数据，支持在机器人位置进行插值提取4×4栅格数据。

## 功能特性

- 🌊 支持加载海风、海浪、洋流的NetCDF格式数据
- 📊 自动处理不同分辨率的栅格数据
- 🔄 基于scipy的高效插值算法
- 📍 在机器人位置提取4×4栅格数据（栅格大小0.2×0.2）
- 📈 数据可视化功能
- 🛠️ 完整的测试和示例代码

## 文件结构

```
ocean_data_test/
├── __init__.py                 # 模块初始化文件
├── ocean_data_loader.py        # 核心数据加载器类
├── test_ocean_data.py          # 测试脚本
├── requirements.txt            # 依赖包列表
├── README.md                   # 本说明文件
└── ocean_data/                 # 数据文件目录（需要手动创建或运行测试脚本自动创建）
    ├── wind.nc                 # 海风数据文件
    ├── wave.nc                 # 海浪数据文件
    ├── current.nc              # 洋流数据文件
    └── README.md               # 数据格式说明
```

## 安装依赖

```bash
cd ocean_data_test
pip install -r requirements.txt
```

## 使用方法

### 1. 快速开始

```python
from ocean_data_test.ocean_data_loader import OceanDataLoader

# 创建数据加载器
loader = OceanDataLoader()

# 加载数据文件
loader.load_wind_data('./ocean_data/wind.nc')
loader.load_wave_data('./ocean_data/wave.nc')
loader.load_current_data('./ocean_data/current.nc')

# 设置插值器
loader.setup_interpolators(time_index=0)

# 在机器人位置提取4×4栅格数据
robot_lat, robot_lon = 30.0, 120.0
grid_data = loader.extract_grid_data(robot_lat, robot_lon)

# 查看提取的数据
for data_type, type_data in grid_data.items():
    print(f"{data_type} 数据:")
    for var_name, var_data in type_data.items():
        print(f"  {var_name}: 形状={var_data.shape}")
```

### 2. 运行测试脚本

```bash
cd ocean_data_test
python test_ocean_data.py
```

测试脚本会：
- 检查数据文件是否存在
- 可选择创建示例数据文件
- 加载和测试所有功能
- 生成可视化结果

## 数据格式要求

### NetCDF文件格式
- 文件扩展名：`.nc`
- 坐标系统：包含经纬度坐标（lat/latitude, lon/longitude）
- 时间维度：可选（time）

### 常见变量名

#### 海风数据
- `u10`, `v10`: 10米高度风速分量 (m/s)
- `wind_speed`: 风速 (m/s)
- `wind_direction`: 风向 (度)

#### 海浪数据
- `swh`: 有效波高 (m)
- `mwd`: 平均波向 (度)
- `mwp`: 平均波周期 (s)

#### 洋流数据
- `u`, `v`: 海流速度分量 (m/s)
- `current_speed`: 流速 (m/s)
- `current_direction`: 流向 (度)

## 核心类和方法

### OceanDataLoader 类

#### 主要方法

- `load_wind_data(file_path)`: 加载海风数据
- `load_wave_data(file_path)`: 加载海浪数据
- `load_current_data(file_path)`: 加载洋流数据
- `setup_interpolators(time_index=0)`: 设置插值器
- `extract_grid_data(robot_lat, robot_lon)`: 提取栅格数据
- `get_data_summary()`: 获取数据摘要

#### 配置参数

- `grid_size = 0.2`: 栅格大小（度）
- `grid_count = 4`: 栅格数量（4×4）

## 插值算法

使用scipy的`RegularGridInterpolator`进行线性插值：
- 方法：线性插值（`method='linear'`）
- 边界处理：超出边界时填充0（`bounds_error=False, fill_value=0`）
- 支持不同分辨率数据的统一处理

## 输出数据格式

`extract_grid_data()`方法返回的数据结构：

```python
{
    'wind': {
        'u10': numpy.ndarray(4, 4),  # 4×4栅格的u分量风速
        'v10': numpy.ndarray(4, 4),  # 4×4栅格的v分量风速
    },
    'wave': {
        'swh': numpy.ndarray(4, 4),  # 4×4栅格的有效波高
        'mwd': numpy.ndarray(4, 4),  # 4×4栅格的平均波向
    },
    'current': {
        'u': numpy.ndarray(4, 4),    # 4×4栅格的u分量流速
        'v': numpy.ndarray(4, 4),    # 4×4栅格的v分量流速
    }
}
```

## 可视化功能

测试脚本包含数据可视化功能，会生成：
- 热力图显示各海洋要素的空间分布
- 机器人位置标记
- 保存为PNG格式图像

## 注意事项

1. **数据文件路径**：确保.nc文件路径正确
2. **坐标系统**：支持常见的经纬度坐标命名（lat/latitude, lon/longitude）
3. **内存使用**：大数据文件可能需要较多内存
4. **时间索引**：多时间步数据需要指定时间索引
5. **数据质量**：插值结果依赖于原始数据的质量和分辨率

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多海洋要素类型
- 添加时间序列处理
- 实现并行处理大数据集
- 添加数据质量检查
- 支持不同的插值方法

## 故障排除

### 常见问题

1. **ImportError**: 安装所需依赖包
   ```bash
   pip install numpy scipy xarray netcdf4 matplotlib
   ```

2. **文件不存在**: 检查数据文件路径和文件名

3. **坐标名称错误**: 检查NetCDF文件中的坐标变量名称

4. **内存不足**: 对于大文件，考虑使用dask进行分块处理

## 联系信息

如有问题或建议，请联系开发团队。