#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海洋数据可视化脚本
用于从提取的海洋数据中创建可视化图表
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.colors as mcolors
from mpl_toolkits.axes_grid1 import make_axes_locatable

# 方法1：全局设置（永久生效） 中文不显示问题
plt.rcParams['font.family'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
class OceanDataVisualizer:
    """
    海洋数据可视化工具
    用于绘制提取的海洋数据
    """
    
    def __init__(self, data_path="extracted_ocean_data.pkl"):
        """
        初始化可视化工具
        
        Args:
            data_path: 提取数据的路径
        """
        self.data_path = data_path
        self.data = None
        self.load_data()
        
        # 设置颜色映射
        self.cmap_temp = plt.cm.RdYlBu_r  # 温度色图
        self.cmap_current = plt.cm.viridis  # 流速色图
        self.cmap_wave = plt.cm.Blues  # 浪高色图
        self.cmap_wind = plt.cm.YlOrRd  # 风速色图
        
    def load_data(self):
        """
        加载提取的数据
        """
        try:
            with open(self.data_path, 'rb') as f:
                self.data = pickle.load(f)
            print(f"数据已加载: {self.data_path}")
            print(f"包含数据类型: {list(self.data.keys())}")
        except Exception as e:
            print(f"加载数据失败: {e}")
            self.data = None
    
    def plot_all(self, save_path="ocean_data_visualization.png"):
        """
        绘制所有数据类型的可视化图
        
        Args:
            save_path: 保存图像的路径
        """
        if self.data is None:
            print("没有可用数据")
            return
        
        # 确定需要绘制的子图数量
        n_plots = 0
        plot_configs = []
        
        # 风场数据
        if 'wind' in self.data:
            n_plots += 1
            plot_configs.append(('wind', '风场 (10m)', self.plot_wind_field))
        
        # 浪场数据
        if 'wave' in self.data:
            n_plots += 1
            plot_configs.append(('wave', '有效波高', self.plot_wave_field))
        
        # 流场数据
        if 'current' in self.data:
            # 流速
            n_plots += 1
            plot_configs.append(('current_vel', '表层流速', self.plot_current_field))
            
            # 温度
            if 'water_temp' in self.data['current']['variables']:
                n_plots += 1
                plot_configs.append(('temp', '表层水温', self.plot_temperature_field))
            
            # 盐度
            if 'salinity' in self.data['current']['variables']:
                n_plots += 1
                plot_configs.append(('salinity', '表层盐度', self.plot_salinity_field))
        
        # 创建图形
        n_rows = (n_plots + 1) // 2  # 向上取整
        n_cols = min(2, n_plots)  # 最多2列
        
        # 创建子图，axes表示子图的数组（这是一个对象，用于传递进行绘图），取值为None时表示子图不存在
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
        if n_plots == 1:
            axes = np.array([axes])  # 确保axes是数组
        
        # 扁平化axes数组以便迭代
        if n_rows > 1 and n_cols > 1:
            axes_flat = axes.flatten()
        else:
            axes_flat = axes
        
        # 绘制每个子图
        for i, (plot_type, title, plot_func) in enumerate(plot_configs):
            if i < len(axes_flat):
                ax = axes_flat[i]
                plot_func(ax, title)
        
        # 隐藏多余的子图
        for i in range(len(plot_configs), len(axes_flat)):
            axes_flat[i].axis('off')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图像已保存: {save_path}")
        
        # 显示图像
        plt.show()
    
    def plot_wind_field(self, ax, title):
        """
        绘制风场
        
        Args:
            ax: matplotlib轴对象
            title: 图表标题
        """
        if 'wind' not in self.data:
            ax.text(0.5, 0.5, '无风场数据', ha='center', va='center')
            ax.set_title(title)
            return
        
        wind_data = self.data['wind']
        lats = wind_data['coordinates']['latitude']
        lons = wind_data['coordinates']['longitude']
        
        # 获取u和v分量
        u = wind_data['variables']['u10']['data']
        v = wind_data['variables']['v10']['data']
        
        # 计算风速
        speed = np.sqrt(u**2 + v**2)
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制风速等值线
        cf = ax.contourf(lon_grid, lat_grid, speed, cmap=self.cmap_wind, levels=15)
        
        # 添加颜色条
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="5%", pad=0.1)
        cbar = plt.colorbar(cf, cax=cax)
        cbar.set_label('风速 (m/s)')
        
        # 绘制风向箭头（降采样以避免过于密集）
        skip = max(1, len(lats)//20)  # 根据数据大小调整降采样率
        ax.quiver(lon_grid[::skip, ::skip], lat_grid[::skip, ::skip], 
                 u[::skip, ::skip], v[::skip, ::skip], 
                 scale=50, color='black', alpha=0.7)
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel('经度 (°E)')
        ax.set_ylabel('纬度 (°N)')
        ax.grid(True, linestyle='--', alpha=0.5)
    
    def plot_wave_field(self, ax, title):
        """
        绘制浪场
        
        Args:
            ax: matplotlib轴对象
            title: 图表标题
        """
        if 'wave' not in self.data:
            ax.text(0.5, 0.5, '无浪场数据', ha='center', va='center')
            ax.set_title(title)
            return
        
        wave_data = self.data['wave']
        lats = wave_data['coordinates']['latitude']
        lons = wave_data['coordinates']['longitude']
        
        # 获取有效波高
        swh = wave_data['variables']['swh']['data']
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制有效波高等值线
        cf = ax.contourf(lon_grid, lat_grid, swh, cmap=self.cmap_wave, levels=15)
        
        # 添加颜色条
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="5%", pad=0.1)
        cbar = plt.colorbar(cf, cax=cax)
        cbar.set_label('有效波高 (m)')
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel('经度 (°E)')
        ax.set_ylabel('纬度 (°N)')
        ax.grid(True, linestyle='--', alpha=0.5)
    
    def plot_current_field(self, ax, title):
        """
        绘制流场
        
        Args:
            ax: matplotlib轴对象
            title: 图表标题
        """
        if 'current' not in self.data:
            ax.text(0.5, 0.5, '无流场数据', ha='center', va='center')
            ax.set_title(title)
            return
        
        current_data = self.data['current']
        lats = current_data['coordinates']['latitude']
        lons = current_data['coordinates']['longitude']
        
        # 获取u和v分量
        if 'water_u' in current_data['variables'] and 'water_v' in current_data['variables']:
            u = current_data['variables']['water_u']['data']
            v = current_data['variables']['water_v']['data']
            
            # 处理多维数组
            if len(u.shape) > 2:
                # 假设前两个维度是时间和深度，取第一个时间和深度层
                u = u[0, 0]
                v = v[0, 0]
            
            # 计算流速
            speed = np.sqrt(u**2 + v**2)
            
            # 创建网格
            lon_grid, lat_grid = np.meshgrid(lons, lats)
            
            # 绘制流速等值线
            cf = ax.contourf(lon_grid, lat_grid, speed, cmap=self.cmap_current, levels=15)
            
            # 添加颜色条
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.1)
            cbar = plt.colorbar(cf, cax=cax)
            cbar.set_label('流速 (m/s)')
            
            # 绘制流向箭头（降采样以避免过于密集）
            skip = max(1, len(lats)//25)  # 根据数据大小调整降采样率
            ax.quiver(lon_grid[::skip, ::skip], lat_grid[::skip, ::skip], 
                     u[::skip, ::skip], v[::skip, ::skip], 
                     scale=5, color='black', alpha=0.7)
        else:
            ax.text(0.5, 0.5, '无流速数据', ha='center', va='center')
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel('经度 (°E)')
        ax.set_ylabel('纬度 (°N)')
        ax.grid(True, linestyle='--', alpha=0.5)
    
    def plot_temperature_field(self, ax, title):
        """
        绘制水温场
        
        Args:
            ax: matplotlib轴对象
            title: 图表标题
        """
        if 'current' not in self.data or 'water_temp' not in self.data['current']['variables']:
            ax.text(0.5, 0.5, '无水温数据', ha='center', va='center')
            ax.set_title(title)
            return
        
        current_data = self.data['current']
        lats = current_data['coordinates']['latitude']
        lons = current_data['coordinates']['longitude']
        
        # 获取水温数据
        temp = current_data['variables']['water_temp']['data']
        
        # 处理多维数组
        if len(temp.shape) > 2:
            # 假设前两个维度是时间和深度，取第一个时间和深度层
            temp = temp[0, 0]
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制水温等值线
        cf = ax.contourf(lon_grid, lat_grid, temp, cmap=self.cmap_temp, levels=15)
        
        # 添加颜色条
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="5%", pad=0.1)
        cbar = plt.colorbar(cf, cax=cax)
        cbar.set_label('水温 (°C)')
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel('经度 (°E)')
        ax.set_ylabel('纬度 (°N)')
        ax.grid(True, linestyle='--', alpha=0.5)
    
    def plot_salinity_field(self, ax, title):
        """
        绘制盐度场
        
        Args:
            ax: matplotlib轴对象
            title: 图表标题
        """
        if 'current' not in self.data or 'salinity' not in self.data['current']['variables']:
            ax.text(0.5, 0.5, '无盐度数据', ha='center', va='center')
            ax.set_title(title)
            return
        
        current_data = self.data['current']
        lats = current_data['coordinates']['latitude']
        lons = current_data['coordinates']['longitude']
        
        # 获取盐度数据
        salinity = current_data['variables']['salinity']['data']
        
        # 处理多维数组
        if len(salinity.shape) > 2:
            # 假设前两个维度是时间和深度，取第一个时间和深度层
            salinity = salinity[0, 0]
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # 绘制盐度等值线
        cf = ax.contourf(lon_grid, lat_grid, salinity, cmap='GnBu', levels=15)
        
        # 添加颜色条
        divider = make_axes_locatable(ax)
        cax = divider.append_axes("right", size="5%", pad=0.1)
        cbar = plt.colorbar(cf, cax=cax)
        cbar.set_label('盐度 (psu)')
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel('经度 (°E)')
        ax.set_ylabel('纬度 (°N)')
        ax.grid(True, linestyle='--', alpha=0.5)


def main():
    """
    主函数
    """
    print("海洋数据可视化工具")
    print("=" * 50)
    
    # 创建可视化器
    visualizer = OceanDataVisualizer("extracted_ocean_data.pkl")
    
    # 绘制所有数据
    visualizer.plot_all("ocean_data_visualization.png")


if __name__ == "__main__":
    main()